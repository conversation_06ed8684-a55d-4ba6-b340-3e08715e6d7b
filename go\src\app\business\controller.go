package controller

/**
* 引入控制器
* 请把您使用包用 _ "fincore/app/home/<USER>"导入您编写的包 自动生成路由
* 不是使用则注释掉
* 路由规则：包路径“home/article” + 包中结构体“Cate”转小写+方法名(首字母转小写	_ "fincore/app/business/datacenter"
 即：http://xx.com/home/<USER>/cate/get_list
*/
import (
	_ "fincore/app/business/bankcard"
	_ "fincore/app/business/channel"
	_ "fincore/app/business/common"
	_ "fincore/app/business/customer"
	_ "fincore/app/business/dashboard"
	_ "fincore/app/business/datacenter"
	_ "fincore/app/business/developer"
	_ "fincore/app/business/makecode"
	_ "fincore/app/business/order"
	_ "fincore/app/business/payment"
	_ "fincore/app/business/productrules"
	_ "fincore/app/business/repayment"
	_ "fincore/app/business/risk"
	_ "fincore/app/business/suggestions"
	_ "fincore/app/business/system"
	_ "fincore/app/business/user"
)
