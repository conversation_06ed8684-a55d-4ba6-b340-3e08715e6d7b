package order

import (
	"context"
	"errors"
	"fincore/app/dianziqian/utils"
	"fmt"
	"time"

	"fincore/route/middleware"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"fincore/app/business/risk"
	"fincore/model"
	"fincore/thirdparty/payment/sumpay"
	"fincore/utils/convert"
	"fincore/utils/gform"
	"fincore/utils/lock"
	"fincore/utils/log"
)

type DisbursementType string

const (
	DisbursementTypeAuto   DisbursementType = "系统自动"
	DisbursementTypeManual DisbursementType = "人工放款"
)

// ProcessOrderDisbursement 处理订单放款流程
func (s *Service) ProcessOrderDisbursement(ctx *gin.Context, orderNo string, operator *middleware.UserClaims) (any, error) {
	// 执行人工放款
	return s.executeDisbursement(orderNo, int(operator.ID), operator.Name, DisbursementTypeManual)
}

// ExecuteDisbursementForCompensation 自动放款补偿任务专用方法
func (s *Service) ExecuteDisbursementForCompensation(orderNo string, userID int) (any, error) {
	// 等待用户创建订单流程结束
	key := GenerateCreateOrderLockKey(userID)
	orderLock := lock.GetLockWithLogger(key, s.logger)
	orderLock.Lock()
	defer orderLock.Unlock()

	// 执行系统自动放款，使用系统操作员ID
	return s.executeDisbursement(orderNo, 0, "系统自动放款补偿", DisbursementTypeAuto)
}

// executeDisbursement 统一放款方法
func (s *Service) executeDisbursement(
	orderNo string,
	operatorID int,
	operatorName string,
	actionPrefix DisbursementType,
) (resp any, err error) {

	// 订单维度加锁
	key := fmt.Sprintf("%s_%s", OrderDisbursementLockKey, orderNo)
	orderLock := lock.GetLockWithLogger(key, s.logger)
	orderLock.Lock()
	defer orderLock.Unlock()

	s.logger.WithFields(
		log.String("order_no", orderNo),
		log.Int("operator_id", operatorID),
		log.String("operation", "process_disbursement"),
	).Info("开始处理订单放款流程")

	// 查询订单信息
	order, err := s.orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, errors.New("订单不存在")
	}

	// 验证订单状态
	if !order.CanStartDisbursement() {
		return nil, fmt.Errorf("订单状态不允许放款，当前状态: %s", order.GetStatusDescription())
	}

	// 获取支付服务
	paymentService, err := s.getPaymentService(s.ctx)
	if err != nil {
		return nil, fmt.Errorf("获取支付服务失败: %v", err)
	}

	// 3. 并发获取用户信息和银行卡信息
	var (
		g        *errgroup.Group
		card     *model.BusinessBankCards
		ctx1     context.Context
		userinfo *model.BusinessAppAccount
	)
	ctx1, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	g, _ = errgroup.WithContext(ctx1)

	// 获取用户信息
	g.Go(func() error {
		businessAppAccountService := model.NewBusinessAppAccountService()
		userinfo, err = businessAppAccountService.GetBusinessAppAccountByID(int64(order.UserID))
		if err != nil {
			return err
		}
		return nil
	})

	// 获取银行卡信息
	g.Go(func() error {
		// 查询合同信息
		contract, err := s.contractModel.GetContractByID(int(order.ContractID))
		if err != nil {
			return err
		}
		if contract.BankCardID == 0 {
			return errors.New("未找到合同绑定的放款卡")
		}
		card, err = s.bankCardModel.GetBankCardByID(contract.BankCardID)
		return err
	})

	if err = g.Wait(); err != nil {
		return nil, fmt.Errorf("获取用户信息或银行卡信息失败: %v", err)
	}

	// 检查放款流水记录
	transactions, err := s.transactionModel.GetTransactionsByConditions(model.TransactionCondition{
		OrderID: int(order.ID),
		Type:    model.TransactionTypeDisbursement,
	})

	if err != nil {
		return nil, fmt.Errorf("查询放款流水记录失败: %v", err)
	}

	// 允许重复放款
	allowRepeatDisbursement := false
	// 检查是否存在已放款的交易记录
	for _, transaction := range transactions {
		switch transaction.Status {
		case model.TransactionStatusSubmitted:
			// 处理中状态，不允许重复提交
			return nil, errors.New("放款请求已提交请刷新放款状态")
		case model.TransactionStatusSuccess:
			// 已成功状态，不允许重复放款
			return nil, errors.New("该订单已成功放款，不能重复操作")
		case model.TransactionStatusFailed: // 失败状态，允许重新放款
		case model.TransactionStatusCanceled: // 已撤回状态，允许重新放款
		case model.TransactionStatusPending: // 待提交状态，允许重新放款
			allowRepeatDisbursement = true
		}
	}

	if allowRepeatDisbursement {
		s.logger.WithFields(
			log.String("order_no", order.OrderNo),
			log.String("operation", "allow_repeat_disbursement"),
		).Info("允许重复放款")
	}

	// 创建支付流水
	transaction := &model.BusinessPaymentTransactions{
		TransactionNo:    GenerateDisbursementTransactionNo(),
		OrderID:          int(order.ID),
		OrderNo:          order.OrderNo,
		UserID:           int(order.UserID),
		PaymentChannelID: int(*order.PaymentChannelID),
		Type:             model.TransactionTypeDisbursement,
		Amount:           model.Decimal(order.LoanAmount),
		Status:           model.TransactionStatusPending,
	}

	err = s.transactionModel.CreateTransaction(transaction)
	if err != nil {
		return nil, fmt.Errorf("创建支付流水失败: %v", err)
	}

	// 3. 调用支付服务进行放款
	result, err := paymentService.TransferToCard(sumpay.DisbursementParams{
		OrderNo:     order.OrderNo,
		Realname:    userinfo.Name,
		IDNo:        userinfo.IDCard,
		CardNo:      card.BankCardNo,
		OrderAmount: fmt.Sprintf("%.2f", order.LoanAmount),
	})

	if err != nil {
		transaction.Status = model.TransactionStatusFailed
		err = s.transactionModel.UpdateTransactionStatus(
			nil,
			model.UpdateTransactionStatusResultWhere{
				TransactionNo: transaction.TransactionNo,
			},
			map[string]interface{}{
				"status":        model.TransactionStatusFailed,
				"error_message": err.Error(),
			})
		if err != nil {
			return nil, err
		}

		// 放款失败，记录日志
		action := fmt.Sprintf("%s失败", "提交"+string(actionPrefix))
		details := fmt.Sprintf("订单编号: %s, 放款金额: %.2f, 失败原因: %v",
			order.OrderNo, float64(order.Principal), err)
		s.createOperationLog(int(order.ID), operatorID, operatorName, action, details)
		return nil, fmt.Errorf("%s失败, 失败原因: %v", actionPrefix, err)
	}

	var (
		ret        any
		e          error
		resultJson string
	)

	// raw result
	resultJson, err = utils.StructToJson(result)
	if err != nil {
		return nil, err
	}

	// 判断 result 是否存在 response 字段
	if _, ok := result["response"]; ok {
		RespResponse := &sumpay.PrivateAgentPaySuccessBodyResponse{}
		e = convert.JsonToStruct(result["response"].(string), RespResponse)
		if e != nil {
			return nil, fmt.Errorf("转换结果失败: %v", e)
		}

		ret = &sumpay.PrivateAgentPaySuccessResponse{}
		delete(result, "response")
		e = convert.MapToStruct(result, ret)
		ret.(*sumpay.PrivateAgentPaySuccessResponse).Response = *RespResponse

	} else {
		ret = &sumpay.PrivateAgentPayFailedResponse{}
		e = convert.MapToStruct(result, ret)
	}

	if e != nil {
		return nil, fmt.Errorf("转换结果失败: %v", e)
	}

	switch r := ret.(type) {
	// 提交处理成功，不保证业务上是成功处理
	case *sumpay.PrivateAgentPaySuccessResponse:
		successResponse := r
		if successResponse.RespCode == sumpay.RespCodeSuccess && successResponse.Response.Status == sumpay.RespCodePayCommitSuccess {
			transaction.Status = model.TransactionStatusSubmitted
		} else {
			transaction.Status = model.TransactionStatusFailed
		}

		// 提交成功，记录日志
		action := fmt.Sprintf("%s%s", "已提交", actionPrefix)
		details := fmt.Sprintf("订单编号: %s, 放款金额: %.2f",
			order.OrderNo, float64(order.Principal))
		s.createOperationLog(int(order.ID), operatorID, operatorName, action, details)

		updateMap := map[string]interface{}{
			"channel_transaction_no": successResponse.Response.TradeNo,
			"status":                 model.TransactionStatusSubmitted,
			"callback_result":        resultJson,
		}

		if transaction.Status == model.TransactionStatusFailed {
			err = fmt.Errorf("%s失败, %s", actionPrefix, successResponse.RespMsg)
			resp = nil
			updateMap["status"] = model.TransactionStatusFailed
			updateMap["error_message"] = successResponse.RespMsg
			updateMap["error_code"] = successResponse.RespCode

		} else {
			updateMap["status"] = model.TransactionStatusSubmitted
			resp = map[string]interface{}{
				"resp_code": successResponse.RespCode,
				"resp_msg":  successResponse.RespMsg,
			}
		}

		err = s.transactionModel.UpdateTransactionStatus(
			nil,
			model.UpdateTransactionStatusResultWhere{
				TransactionNo: transaction.TransactionNo,
			},
			updateMap,
		)
		if err != nil {
			return nil, err
		}

		// 异步刷新状态
		go func() {
			defer func() {
				if r := recover(); r != nil {
					s.logger.WithFields(
						log.String("order_no", order.OrderNo),
						log.Any("panic_info", r),
					).Error("异步刷新放款状态发生panic")
				}
			}()

			// 等待3秒
			time.Sleep(3 * time.Second)

			// 创建执行阶段的超时控制
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			done := make(chan error, 1)
			go func() {
				done <- s.RefreshDisbursementStatus(order.OrderNo)
			}()

			select {
			case err := <-done:
				// 正常完成
				if err != nil {
					s.logger.WithFields(
						log.String("order_no", order.OrderNo),
						log.ErrorField(err),
					).Error("异步刷新放款状态失败")
				} else {
					s.logger.WithFields(
						log.String("order_no", order.OrderNo),
					).Info("异步刷新放款状态成功")
				}
			case <-ctx.Done():
				// 处理超时
				s.logger.WithFields(
					log.String("order_no", order.OrderNo),
					log.String("reason", "execution_timeout"),
					log.Duration("timeout_duration", 5*time.Second),
				).Warn("刷新放款状态执行超时，优雅退出")
				return
			}
		}()

		return resp, err

		// 提交失败，比如验证签名失败
	case *sumpay.PrivateAgentPayFailedResponse:
		failedResponse := r
		updateMap := map[string]interface{}{
			"status":          model.TransactionStatusFailed,
			"error_message":   failedResponse.RespMsg,
			"error_code":      failedResponse.RespCode,
			"callback_result": resultJson,
		}

		err = s.transactionModel.UpdateTransactionStatus(
			nil,
			model.UpdateTransactionStatusResultWhere{
				TransactionNo: transaction.TransactionNo,
			},
			updateMap,
		)
		if err != nil {
			return nil, err
		}
		// 放款失败，记录日志
		action := fmt.Sprintf("%s%s", "已提交", actionPrefix)
		details := fmt.Sprintf("订单编号: %s, 放款金额: %.2f, 失败原因: %v",
			order.OrderNo, float64(order.Principal), failedResponse.RespMsg)
		s.createOperationLog(int(order.ID), operatorID, operatorName, action, details)
		return nil, fmt.Errorf("%s失败, 失败原因: %v", actionPrefix, failedResponse.RespMsg)

	default:
		return nil, fmt.Errorf("未知的返回结果类型: %T", ret)
	}

}

// updateOrderRiskResult 更新订单风控结果
func (s *Service) updateOrderRiskResult(tx gform.IOrm, orderID int64, riskResult *risk.RiskEvaluationResponse) error {
	var handler gform.IOrm
	if tx != nil {
		handler = tx
	} else {
		handler = model.DB(model.WithContext(s.ctx))
	}

	if riskResult == nil {
		return fmt.Errorf("风控结果不能为空")
	}

	// 更新订单的风控结果字段
	updateData := map[string]interface{}{
		"risk_control_results": riskResult.RiskResult,
		"updated_at":           time.Now(),
	}

	_, err := handler.Table("business_loan_orders").
		Where("id", orderID).
		Data(updateData).
		Update()
	if err != nil {
		return fmt.Errorf("更新订单风控结果失败: %v", err)
	}

	s.logger.WithFields(
		log.OrderID(orderID),
		log.Int("risk_score", riskResult.RiskScore),
		log.String("operation", "update_risk_result"),
	).Info("更新订单风控结果成功")
	return nil
}

// CloseOrderWithReason 关闭订单并记录原因
func (s *Service) CloseOrderWithReason(orderID int64, operatorID int, reason, details string) error {
	s.logger.WithFields(
		log.OrderID(orderID),
		log.String("reason", reason),
		log.String("operation", "close_order"),
	).Info("关闭订单")

	operatorName := "系统"
	if operatorID > 0 {
		operatorName = fmt.Sprintf("操作员%d", operatorID)
	}

	// 风控关单
	reasonCode := model.OrderStatusClosedByRisk
	closureRemarks := fmt.Sprintf("%s - %s", reason, details)

	// 调用新的关闭订单方法
	err := s.CloseOrderWithReasonCode(int(orderID), reasonCode, closureRemarks, operatorID, operatorName)
	if err != nil {
		return fmt.Errorf("关闭订单失败: %v", err)
	}

	return nil
}

// CloseOrderWithReasonCode 使用原因码关闭订单
func (s *Service) CloseOrderWithReasonCode(orderID int, reasonCode int, closureRemarks string, operatorID int, operatorName string) error {
	if orderID <= 0 {
		return errors.New("订单ID无效")
	}
	if reasonCode < model.OrderStatusClosedByRisk || reasonCode > model.OrderStatusClosedByCustomerDisagree {
		return errors.New("关闭原因码无效，必须在-1 到 7范围内")
	}

	// 在事务中关闭订单并取消还款计划
	err := s.CloseOrder(orderID, reasonCode, closureRemarks)
	if err != nil {
		return fmt.Errorf("关闭订单失败: %v", err)
	}

	var details string
	// 记录操作日志
	go func() {
		defer func() {
			if r := recover(); r != nil {
				s.logger.WithFields(
					log.OrderID(orderID),
					log.String("operation", "close_order_log"),
					log.Any("panic_info", r),
				).Error("记录关闭订单日志发生panic")
			}
		}()

		// 获取订单信息用于日志记录
		order, err := s.orderModel.GetOrderByID(orderID)
		if err != nil {
			s.logger.WithFields(
				log.OrderID(orderID),
				log.String("operation", "get_order_for_log"),
				log.ErrorField(err),
			).Error("获取订单信息失败，无法记录详细日志")
			return
		}

		// 构建详细的操作日志
		reasonText := s.orderModel.GetClosureReasonText(reasonCode)
		details = fmt.Sprintf("订单号: %s, 状态变更: %s -> 交易关闭, 关闭原因: %s(%d), 备注: %s, 操作人: %s(ID:%d)",
			order.OrderNo,
			order.GetStatusDescription(),
			reasonText,
			reasonCode,
			closureRemarks,
			operatorName,
			operatorID)

		s.createOperationLog(orderID, operatorID, operatorName, "关闭订单", details)
	}()

	s.logger.WithFields(
		log.OrderID(orderID),
		log.String("details", details),
		log.String("operation", "close_order_success"),
	).Info("订单关闭成功")

	return nil
}

// CloseOrder 关闭订单
func (s *Service) CloseOrder(id int, reasonCode int, closureRemarks string) error {
	// 先查询当前订单状态
	order, err := s.orderModel.GetOrderByID(id)
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}

	if !order.CanClose() {
		return fmt.Errorf("订单状态不允许关闭，当前状态: %s", order.GetStatusDescription())
	}

	// 使用事务处理订单关闭和还款计划取消
	return model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 更新订单状态
		orderData := map[string]interface{}{
			"status":             model.OrderStatusClosed,
			"reason_for_closure": reasonCode,
			"closure_remarks":    closureRemarks,
			"completed_at":       time.Now(),
			"updated_at":         time.Now(),
		}

		_, err := tx.Table("business_loan_orders").Where("id", id).Data(orderData).Update()
		if err != nil {
			return fmt.Errorf("更新订单状态失败: %v", err)
		}

		// 2. 取消该订单的所有还款计划账单（状态改为4-已取消）
		billData := map[string]interface{}{
			"status":     4, // 4-已取消
			"updated_at": time.Now(),
		}

		// 风控关单不刷新风控数据
		if reasonCode != model.OrderStatusClosedByRisk {
			s.RefreshRiskData(tx, int(order.UserID))
		}

		_, err = tx.Table("business_repayment_bills").Where("order_id", id).Data(billData).Update()
		if err != nil {
			return fmt.Errorf("取消还款计划失败: %v", err)
		}

		return nil
	})
}

// AssignOrderToSales 分配订单给业务员
func (s *Service) AssignOrderToSales(orderID int64, salesID int, operatorID int, assignType string) error {
	s.logger.WithFields(
		log.OrderID(orderID),
		log.Int("sales_id", salesID),
		log.String("assign_type", assignType),
		log.String("operation", "assign_to_sales"),
	).Info("分配订单给业务员")

	// 1. 验证订单状态
	orderData, err := model.DB().Table("business_loan_orders").
		Where("id", orderID).
		First()
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if len(orderData) == 0 {
		return fmt.Errorf("订单不存在")
	}

	// 2. 验证业务员是否存在
	salesData, err := model.DB().Table("business_account").
		Where("id", salesID).
		Where("status", 0). // 0-正常状态
		First()
	if err != nil {
		return fmt.Errorf("查询业务员失败: %v", err)
	}
	if len(salesData) == 0 {
		return fmt.Errorf("业务员不存在或已禁用")
	}

	// 3. 更新订单分配信息
	updateData := map[string]interface{}{
		"sales_assignee_id": salesID,
		"updated_at":        time.Now(),
	}

	_, err = model.DB().Table("business_loan_orders").
		Where("id", orderID).
		Data(updateData).
		Update()
	if err != nil {
		return fmt.Errorf("更新订单分配失败: %v", err)
	}

	// 4. 记录操作日志
	operatorName := "系统"
	if operatorID > 0 {
		operatorName = fmt.Sprintf("操作员%d", operatorID)
	}

	salesName := convert.GetStringFromMap(salesData, "name")
	action := fmt.Sprintf("分配订单(%s)", assignType)
	details := fmt.Sprintf("分配给业务员: %s (ID: %d)", salesName, salesID)
	s.createOperationLog(int(orderID), operatorID, operatorName, action, details)

	s.logger.WithFields(
		log.OrderID(orderID),
		log.String("sales_name", salesName),
		log.String("operation", "assign_success"),
	).Info("订单分配成功")
	return nil
}

// ClaimOrderBySales 业务员认领订单
func (s *Service) ClaimOrderBySales(orderID int64, salesID int) error {
	return s.AssignOrderToSales(orderID, salesID, 0, "业务员认领")
}

// CancelClaimOrderBySales 取消认领订单
func (s *Service) CancelClaimOrderBySales(orderID int64, currentUserID int) error {
	s.logger.WithFields(
		log.OrderID(orderID),
		log.Int("current_user_id", currentUserID),
		log.String("operation", "cancel_claim"),
	).Info("取消认领订单")

	// 1. 验证订单是否存在
	orderData, err := model.DB().Table("business_loan_orders").
		Where("id", orderID).
		First()
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if len(orderData) == 0 {
		return fmt.Errorf("订单不存在")
	}

	// 2. 检查权限：当前订单的业务员ID是否为当前用户ID
	assignedSalesID := convert.GetIntFromMap(orderData, "sales_assignee_id", 0)
	if assignedSalesID == 0 {
		return fmt.Errorf("订单未分配给任何业务员，无法取消认领")
	}
	if assignedSalesID != currentUserID {
		return fmt.Errorf("权限不足，只能取消认领自己的订单")
	}

	// 3. 将订单的业务员ID设置为null
	updateData := map[string]interface{}{
		"sales_assignee_id": 0,
		"updated_at":        time.Now(),
	}

	_, err = model.DB().Table("business_loan_orders").
		Where("id", orderID).
		Data(updateData).
		Update()
	if err != nil {
		return fmt.Errorf("取消认领失败: %v", err)
	}

	// 4. 记录操作日志
	s.createOperationLog(int(orderID), currentUserID, "业务员", "取消认领订单", "业务员主动取消认领")

	s.logger.WithFields(
		log.OrderID(orderID),
		log.String("operation", "cancel_claim_success"),
	).Info("取消认领订单成功")
	return nil
}

// BatchAssignOrdersToSales 批量分配订单给业务员
func (s *Service) BatchAssignOrdersToSales(orderIDs []int64, salesID int, operatorID int) map[string]interface{} {
	// 分配结果统计
	type AssignResult struct {
		OrderID int64  `json:"order_id"`
		Success bool   `json:"success"`
		Message string `json:"message"`
	}

	var results []AssignResult
	successCount := 0
	failureCount := 0

	// 逐个分配订单
	for _, orderID := range orderIDs {
		err := s.AssignOrderToSales(orderID, salesID, operatorID, "管理员分配")
		if err != nil {
			results = append(results, AssignResult{
				OrderID: orderID,
				Success: false,
				Message: err.Error(),
			})
			failureCount++
		} else {
			results = append(results, AssignResult{
				OrderID: orderID,
				Success: true,
				Message: "分配成功",
			})
			successCount++
		}
	}

	// 返回批量分配结果
	return map[string]interface{}{
		"total_count":   len(orderIDs),
		"success_count": successCount,
		"failure_count": failureCount,
		"results":       results,
	}
}

// UpdateOrderChannel 修改订单渠道
func (s *Service) UpdateOrderChannel(orderID int64, channelID int, operatorID int, operatorName string) error {
	// 1. 验证订单是否存在
	order, err := s.orderModel.GetOrderByID(int(orderID))
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	// 2. 检查订单状态是否允许修改渠道
	// 只有待放款状态的订单才能修改渠道
	if order.Status != int8(model.OrderStatusPendingDisbursement) {
		return fmt.Errorf("订单状态不允许修改渠道，当前状态: %s", order.GetStatusDescription())
	}

	// 3. 验证渠道是否存在且有效
	channel, err := s.channelModelService.GetChannelByID(channelID)
	if err != nil {
		return fmt.Errorf("查询渠道失败: %v", err)
	}
	if channel == nil {
		return fmt.Errorf("渠道不存在")
	}

	// 检查渠道是否启用
	if channel.ChannelStatus == 0 {
		return fmt.Errorf("渠道未启用，无法使用")
	}

	// 4. 检查是否真的需要更新（避免无意义的更新）
	if int(order.ChannelID) == channelID {
		return fmt.Errorf("订单渠道未发生变化")
	}

	// 5. 更新订单渠道
	updateData := map[string]interface{}{
		"channel_id": channelID,
		"updated_at": time.Now(),
	}

	_, err = model.DB().Table("business_loan_orders").
		Where("id", orderID).
		Data(updateData).
		Update()
	if err != nil {
		return fmt.Errorf("更新订单渠道失败: %v", err)
	}

	// 6. 记录操作日志
	oldChannelName := "未知渠道"
	if oldChannel, err := s.channelModelService.GetChannelByID(int(order.ChannelID)); err == nil && oldChannel != nil {
		oldChannelName = oldChannel.ChannelName
	}

	newChannelName := channel.ChannelName

	logContent := fmt.Sprintf("修改订单渠道: %s(ID:%d) -> %s(ID:%d)",
		oldChannelName, order.ChannelID, newChannelName, channelID)

	// 记录操作日志（createOperationLog没有返回值）
	s.createOperationLog(int(orderID), operatorID, operatorName, "修改渠道", logContent)

	return nil
}

// CreateOrderRemark 新增订单备注
func (s *Service) CreateOrderRemark(orderID int64, content string, userID int64) error {
	// 1. 验证订单是否存在
	orderInfo, err := s.orderModel.GetOrderByID(int(orderID))
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if orderInfo == nil {
		return fmt.Errorf("订单不存在")
	}

	// 2. 创建备注记录
	currentTime := time.Now().Unix()
	remarkData := map[string]interface{}{
		"order_id":    orderID,
		"content":     content,
		"user_id":     userID,
		"create_time": currentTime,
		"update_time": currentTime,
	}

	// 3. 插入备注记录
	remarkID, err := model.DB().Table("business_order_remarks").Data(remarkData).InsertGetId()
	if err != nil {
		return fmt.Errorf("插入备注记录失败: %v", err)
	}

	if remarkID == 0 {
		return fmt.Errorf("获取备注ID失败")
	}

	// 4. 查询用户信息获取用户名
	userInfo, err := model.DB().Table("business_account").Where("id", userID).Fields("username").First()
	userName := "未知用户"
	if err == nil && len(userInfo) > 0 {
		if username, ok := userInfo["username"].(string); ok && username != "" {
			userName = username
		}
	}

	// 5. 记录操作日志
	operationLog := map[string]interface{}{
		"order_id":      orderID,
		"operator_id":   userID,
		"operator_name": userName,
		"action":        "新增备注",
		"details":       fmt.Sprintf("新增订单备注：%s", content),
		"created_at":    currentTime,
	}
	_, err = model.DB().Table("business_order_operation_logs").Data(operationLog).Insert()
	if err != nil {
		// 操作日志记录失败不影响主流程，只记录错误
		s.logger.WithFields(
			log.String("operation", "create_operation_log"),
			log.ErrorField(err),
		).Error("记录操作日志失败")
	}

	// 6. 备注创建成功
	return nil
}

// EarlySettleOrder 提前结清订单
func (s *Service) EarlySettleOrder(orderNo string, operatorID int, operatorName string) error {
	if orderNo == "" {
		return errors.New("订单编号不能为空")
	}

	// 1. 根据订单编号查询订单信息
	order, err := s.orderModel.GetOrderByOrderNo(orderNo)
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return fmt.Errorf("订单不存在")
	}

	// 2. 验证订单状态，只有放款中(1)或还款中状态的订单才能提前结清
	if order.Status != model.OrderStatusDisbursed {
		return fmt.Errorf("订单状态不允许提前结清，当前状态: %s", order.GetStatusDescription())
	}

	// 3. 在事务中执行提前结清操作
	return model.DB(model.WithContext(s.ctx)).Transaction(func(tx gform.IOrm) error {
		// 3.1 更新订单状态为交易完成
		orderData := map[string]interface{}{
			"status":       model.OrderStatusCompleted,
			"completed_at": time.Now(),
			"updated_at":   time.Now(),
		}

		_, err := tx.Table("business_loan_orders").Where("id", order.ID).Data(orderData).Update()
		if err != nil {
			return fmt.Errorf("更新订单状态失败: %v", err)
		}

		// 3.2 更新该订单的所有还款计划状态为提前结清
		billData := map[string]interface{}{
			"status":     model.RepaymentBillStatusEarlySettlement, // 8-提前结清
			"updated_at": time.Now(),
		}

		_, err = tx.Table("business_repayment_bills").Where("order_id", order.ID).Data(billData).Update()
		if err != nil {
			return fmt.Errorf("更新还款计划状态失败: %v", err)
		}

		// 3.3 记录操作日志
		operationLog := map[string]interface{}{
			"order_id":      order.ID,
			"operator_id":   operatorID,
			"operator_name": operatorName,
			"action":        "提前结清",
			"details":       fmt.Sprintf("订单号: %s, 状态变更: %s -> 交易完成", order.OrderNo, order.GetStatusDescription()),
			"created_at":    time.Now(),
		}

		// 更新用户剩余额度, 撤回占用额度
		err = s.repository.UpdateUserRemainingAmount(tx, int(order.UserID), float64(order.LoanAmount), true)
		if err != nil {
			return fmt.Errorf("更新用户剩余额度失败: %v", err)
		}

		// 刷新风控数据
		s.RefreshRiskData(tx, int(order.UserID))

		_, err = tx.Table("business_order_operation_logs").Data(operationLog).Insert()
		if err != nil {
			// 操作日志记录失败不影响主流程，只记录错误
			s.logger.WithFields(
				log.String("operation", "early_settlement_log"),
				log.ErrorField(err),
			).Error("记录提前结清操作日志失败")
		}

		return nil
	})
}
