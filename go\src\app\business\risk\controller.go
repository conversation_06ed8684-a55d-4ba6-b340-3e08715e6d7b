package risk

import (
	"context"
	"net/http"
	"reflect"
	"strconv"
	"time"

	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// RiskController 风控控制器
type RiskController struct{}

func init() {
	// 注册路由到自动路由系统
	gf.Register(&RiskController{}, reflect.TypeOf(RiskController{}).PkgPath())
}

// GetEvaluate 授信评估接口
// 路由: GET /business/risk/riskcontroller/getEvaluate
func (rc *RiskController) GetEvaluate(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetRiskEvaluationSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	var result interface{}
	var err error

	// 直接进行风控评估
	customerID, err := GetCustomerIDFromData(validationResult.Data)
	if err != nil {
		results.Failed(c, "参数错误", err.Error())
		return
	}
	result, err = NewRiskService(c).EvaluateRisk(ctx, model.DB(), int64(customerID))

	if err != nil {
		results.Failed(c, "风控评估失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "评估成功", result, nil)
}

// GetProducts 贷款产品匹配接口
// 路由: GET /business/risk/get_products
func (rc *RiskController) GetProducts(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"channel_id":  c.Query("channel_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetLoanProductsSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 从验证结果中提取customerID
	customerID, err := GetCustomerIDFromData(validationResult.Data)
	if err != nil {
		results.Failed(c, "参数转换失败", err.Error())
		return
	}

	result, err := NewRiskService(c).GetLoanProducts(ctx, int64(customerID))
	if err != nil {
		results.Failed(c, "获取产品列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, nil)
}

// GetProductsByAmount 根据额度获取匹配产品接口
// 路由: GET /business/risk/get_products_by_amount
func (rc *RiskController) GetProductsByAmount(c *gin.Context) {
	// 1. 参数校验
	loanAmountStr := c.Query("loan_amount")
	if loanAmountStr == "" {
		results.Failed(c, "参数错误", "loan_amount参数不能为空")
		return
	}

	// 转换loan_amount为float64
	loanAmount, err := strconv.ParseFloat(loanAmountStr, 64)
	if err != nil {
		results.Failed(c, "参数错误", "loan_amount参数格式错误")
		return
	}

	// 验证loan_amount范围
	if loanAmount < 1.0 || loanAmount > 10000000.0 {
		results.Failed(c, "参数错误", "loan_amount参数超出有效范围(1.0-10000000.0)")
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := NewRiskService(c).GetProductsByAmount(ctx, loanAmount)
	if err != nil {
		results.Failed(c, "获取产品列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, nil)
}

// GetReports 风控报告查询接口
// 路由: GET /business/risk/get_reports
func (rc *RiskController) GetReports(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"start_date":  c.Query("start_date"),
		"end_date":    c.Query("end_date"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetRiskReportSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := NewRiskService(c).GetRiskReports(ctx, validationResult.Data)
	if err != nil {
		// 判断是否为记录不存在的错误
		if err.Error() == "未找到风控评估记录" || err.Error() == "客户不存在" {
			results.Failed(c, err.Error(), nil)
			return
		}
		results.Failed(c, "获取风控报告失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, map[string]interface{}{"customer_id": requestData["customer_id"]})
}

// GetHealth 健康检查接口
// 路由: GET /business/risk/get_health
func (rc *RiskController) GetHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "fincore-risk-service",
		"version": "1.0.0",
		"time":    time.Now().Format("2006-01-02 15:04:05"),
	})
}
