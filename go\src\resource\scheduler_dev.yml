# 定时任务调度器配置文件
# 开发环境配置

scheduler:
  # 调度器基础配置
  name: "fincore-scheduler-dev"       # 调度器名称
  timezone: "Asia/Shanghai"           # 时区设置
  max_concurrent_jobs: 5              # 最大并发任务数（开发环境减少）
  default_timeout: 1800               # 默认任务超时时间（秒）
  force_kill_timeout: 10              # 强制结束超时时间（秒，开发环境更短）

  # 并发控制
  concurrency:
    enable_parallel: true             # 是否启用并行模式
    max_parallel_per_task: 2          # 每个任务最大并行数（开发环境减少）


