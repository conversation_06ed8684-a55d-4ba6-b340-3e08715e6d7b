package service

import (
	<PERSON>an<PERSON><PERSON><PERSON>_Config "fincore/app/dianziqian/config"
	"fincore/global"
)

/* gosdk中的硬编码
const(
	host  ="https://prev.asign.cn/" //测试环境 "https://oapi.asign.cn/" //正式环境
	appId="***"
	privateKey="MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAmt2jO6Z5uwRFM+bdRBXoHcjpdWpUDvwI05py3kZvfcg7ahUJkz4cQvCUaLVMFvH4LyefiA5ER2q7S87653yYMQIDAQABAkBzD4Eb7JA89utDqJ902qHen0t1RU6242LbdMErjEGBvXjzOvLpigUaB8gSR+o1r+damPQJdZWbR4+8EBvK1FORAiEA9EgQVjzmhFXEjsCLo/gWlbnjQhyByV7ZEotosBlm/bsCIQCiS32HTC4AwvBT1gzyr11zWmjxizYtYwV6NryvUE1tAwIgQjh+5UHhI6K0hBZCRJLuXGxl5PghXtttcQ+Fs6dPOh0CIGAq/10WpQPKf4IOCmobw/JAloLajOXkETDUEoaHvPllAiAsYPUVC4fmNW53YeDJiCcQ9yl/WbH6mktfzVl011KBcA=="
)
*/

func InitService() {
	config := Dianziqian_Config.GetInitConfig()
	host := global.App.Config.Aiqian_envs.Pro_host
	app_id := global.App.Config.Aiqian_envs.App_id
	privateKey := global.App.Config.Aiqian_envs.PrivateKey
	config.SetHost(host)
	config.SetAppId(app_id)
	config.SetPrivateKey(privateKey)
}
