// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AAffix: typeof import('@arco-design/web-vue')['Affix']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    AAvatarGroup: typeof import('@arco-design/web-vue')['AvatarGroup']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    ABreadcrumbItem: typeof import('@arco-design/web-vue')['BreadcrumbItem']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACarousel: typeof import('@arco-design/web-vue')['Carousel']
    ACarouselItem: typeof import('@arco-design/web-vue')['CarouselItem']
    ACascader: typeof import('@arco-design/web-vue')['Cascader']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACol: typeof import('@arco-design/web-vue')['Col']
    AConfigProvider: typeof import('@arco-design/web-vue')['ConfigProvider']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AGrid: typeof import('@arco-design/web-vue')['Grid']
    AGridItem: typeof import('@arco-design/web-vue')['GridItem']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    AInputSearch: typeof import('@arco-design/web-vue')['InputSearch']
    ALayout: typeof import('@arco-design/web-vue')['Layout']
    ALayoutContent: typeof import('@arco-design/web-vue')['LayoutContent']
    ALayoutFooter: typeof import('@arco-design/web-vue')['LayoutFooter']
    ALayoutSider: typeof import('@arco-design/web-vue')['LayoutSider']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    AListItemMeta: typeof import('@arco-design/web-vue')['ListItemMeta']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APopconfirm: typeof import('@arco-design/web-vue')['Popconfirm']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    ARadio: typeof import('@arco-design/web-vue')['Radio']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARangePicker: typeof import('@arco-design/web-vue')['RangePicker']
    AResult: typeof import('@arco-design/web-vue')['Result']
    ARow: typeof import('@arco-design/web-vue')['Row']
    AScrollbar: typeof import('@arco-design/web-vue')['Scrollbar']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASkeleton: typeof import('@arco-design/web-vue')['Skeleton']
    ASkeletonLine: typeof import('@arco-design/web-vue')['SkeletonLine']
    ASkeletonShape: typeof import('@arco-design/web-vue')['SkeletonShape']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    AStatistic: typeof import('@arco-design/web-vue')['Statistic']
    AStep: typeof import('@arco-design/web-vue')['Step']
    ASteps: typeof import('@arco-design/web-vue')['Steps']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATreeSelect: typeof import('@arco-design/web-vue')['TreeSelect']
    ATypographyParagraph: typeof import('@arco-design/web-vue')['TypographyParagraph']
    ATypographyText: typeof import('@arco-design/web-vue')['TypographyText']
    ATypographyTitle: typeof import('@arco-design/web-vue')['TypographyTitle']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
