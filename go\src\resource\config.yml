dbconf:
     # 数据库类型 mysql, sqlite3, post<PERSON><PERSON>, sqlserver
     driver: mysql
     #服务器地址 本地建议 127.0.0.1
     hostname: ************
     #端口 默认3306
     hostport: 3306
     #用户名
     username: root
     #密码
     password: root
     #数据库名
     database: fincore
     #数据表前缀
     prefix: 
redis:
     host: 127.0.0.1 # 连接地址
     port: 6379         # 端口
     password:          # 密码
     db: 0              # 数据库编号
     timeout: 15        # 链接超时 单位秒
jwt:
     secret: 3Bde3BGEbYqtqyEUzW3ry8jKFcaPH17fRmTmqE7MDr05Lwj95uruRKrrkb44TJ4s
     jwt_ttl: 43200
app:
     #版本号
     version: 1.3.0
     #环境状态：dev=开发，pro=生产
     env: dev
     #运行服务端口（根据您的实际情况修改）
     port: 8108
     #运行H5服务的端口（根据您的实际情况修改）
     h5port: 444
     #运行服务器的IP地址
     hostname: ************
     #接口合法性验证
     apisecret: gofly@888
     #接口JWT验证、跨域域名-不添加请求时报403 (开发、部署必改)
     allowurl: http://************:9105,http://************:9106,http://************:444,http://localhost:6020,http://localhost:9106
     #token超时时间单位分钟 
     tokenouttime: 10 
     #调用cpu个数
     cpunum: 3
     # Gin 框架在运行的时候默认是debug模式 有： 开发：debug，生产：release，测试模式：test
     runlogtype: debug
     # 配置代码生成时-前端代码根目录位置(开发必改)
     vueobjroot: D:/Project/develop/vue/gofly_enterprise/business
     #配置企业私有仓网址
     companyPrivateHouse: 
     # 配置根域名访问重定向路径,默认是业务端后台
     rootview: webbusiness
     #不需要token-根模块
     noVerifyTokenRoot: resource,webbusiness,webadmin
     #不需要api接口合法性验证-根模块md5加密
     noVerifyAPIRoot: resource,webbusiness,webadmin,uniapp
     #不需要验证token-具体请求路径
     noVerifyToken: /common/uploadfile/get_image,/common/install/index,/common/install/save,/admin/user/login,/admin/user/logout,/admin/user/refreshtoken,/admin/user/get_code,/admin/user/resetPassword,/business/user/login,/business/user/logout,/business/user/refreshtoken,/business/user/get_code,/business/user/resetPassword,/admin/user/get_logininfo,/business/user/get_logininfo,/uniapp/user/loginBySms,/uniapp/captcha/getCaptcha,/uniapp/user/postSms,/uniapp/user/postBySms,/uniapp/user/postBySms,/uniapp/user/getUserInfo,/business/payment/manager/disbursementCallback,/business/payment/manager/PaymentCallback
     #不需要接口合法性-具体请求路径
     noVerifyAPI: /common/install/index,/common/install/save,/business/payment/manager/disbursementCallback
     #邀请页面路径
     invitationPage: /#/pages/login/login
     #前端请求协议
     h5Protocol: http
     # 文件服务地址，后续要改成 oss
     fileServer: http://************:8108
# ==================== 日志系统配置 ====================
# 统一的日志管理系统，支持模块化配置和自动归档
log:
     # 全局日志等级 (debug/info/warn/error)
     # debug: 调试信息，开发环境使用
     # info: 一般信息，生产环境推荐
     # warn: 警告信息
     # error: 错误信息
     level: info

     # 日志根目录，所有日志文件都存放在此目录下
     # 目录结构: ./log/YYYY-MM/YYYY-MM-DD_模块名.log
     # 例如: ./log/2025-07/2025-07-17_app.log
     root_dir: ./log

     # 日志输出格式
     # json: 结构化JSON格式，便于日志分析和处理
     # text: 纯文本格式，便于人工阅读
     format: json

     # 日志输出方式，both: 同时输出到文件和控制台，file: 只输出到文件，console: 只输出到控制台
     output: both

     # 时区设置，支持标准时区名称如：Asia/Shanghai, UTC, America/New_York, Europe/London 等
     timezone: Asia/Shanghai

     # 是否在日志中显示代码调用行号
     # true: 显示文件名和行号，便于调试
     # false: 不显示，提高性能
     show_line: true

     # 日志文件轮转配置
     max_backups: 10    # 保留的旧日志文件最大数量
     max_size: 100      # 单个日志文件最大大小（MB）
     max_age: 30        # 旧日志文件最大保留天数
     compress: true     # 是否压缩旧日志文件（gzip格式）
sms:
     # 短信平台
     platform: dahantc
     # 短信平台账号
     code_account: dh36925
     # 短信平台密码
     code_password: 7f372dbc69aa4b7582cc7a13329509fc
     business_account: dh36926
     business_password: 4904bdcfa78c951709ef17470863d0ac
     # 短信平台url
     apiurl: http://www.dh3t.com/json/sms/BatchSubmit
     # 短信平台签名
     sign:
     #是否启用第三方接口 T启用 F不启用
     isused: T
vbank:
     # 四要素验证第三方API
     apiurl: "http://v.juhe.cn/verifybankcard4/query"
     # 四要素验证第三方API key (购买后获取)
     apikey: ""



# 爱签的基本配置信息
aiqian_envs:
     app_id: "*********"
     pro_host: "https://oapi.asign.cn"
     # pro_host: "https://prev.asign.cn"
     port: 443
     PrivateKey: "MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQC2uFczQgnjOnYRATAG84UV6ZTojzj7/BCnKMqCIlFWYd8WFrmqpOXVmZLRDUjNk33YHPGwEA7l/dc/RA5p2pmNMAHgJVhZbcTV6xyjeU6LDkjyev5XJykruwxS5Y2+EyL4i/pcG6o/A+pI77H7fWNOEPini7GxgjD4bZXv8rJjwYOxE6gcwtnXb7aauRX8ezI62k7g2OUvXRYiZy6oRZoPABWSn/zEN+q3gfGpOFYtozuaUNe/+b1S/sMnts7v11rsgN0UDf/u+DDUVFe924lmeSW21r90oYQyM6XXb8SY2AReu+u8oCGwuIu02G1GlEyTSUjAONEKCGxwqGzZvw/PAgMBAAECggEBAKXSXbCy+e4xm/yKq19jmR/tv6necMSeWS6aok2/fzl50M9nCFCJHdvfZ5I5EB0hAVAj1GMH771hxPoxdTMzo66yJsGYorlmGQBaQr9I07L239TPMgs+CusY8XI5yYz6KP6PakI1CSfvEavnfArUHE84r7C94iFKGc8bBLuh8ar84u2y23HgmIv9I3Zss0quqoarCd6PNI/tyw7NaDpFPUmUOWti3PsgEQhgVo2DCM3WPHPfylPXQ43hZEudkho+HzIE4ztpYXHUOWGLTF+Q5nInNh1MgqxGcBuJuPAWomUk7prSMCav9Kht6Rflnmcw+HoPmWse9zr9ICdECgiygmECgYEA+wjxufMh1p4obHWvTxQpPZLCGsPF8g+WbMDVmmVA3uM6BHMfs3VcFetN1AWu879SLInC5uYC4UC+josAHpfObFEx6pzxja4Zn1Pshp+ArTBFZ/u9jmvvOXSYUoFuOVIZslaX8Ki0/N20ym5RvdFaFx0WwqYBZMjltn5XsT1WDS0CgYEAulWAFdg+b0Z6B9OZ13kSjrEO0WbGhu+gh8Dy8hQqJUjdLSvYMebLY2eHmxL5xG0vN7ARteJ62RZ7bDgFObyQbIOidigz7TiHeId++5SlzBnFCNRPX6eAZTZBfCq90ZveCQhcZ8hIni957RXF5XtBi3Kdqur9EHHgHnPE9ZyqhmsCgYEA0fv1V3oNAB1j6vW2IwvWQ28Tdpf0aDqptWbIRlIUJV0lFrvF9LNix+MAQy5N3g5XinHh2orkNc+Wll2nR+/r96cjfgCx/bV4MVJeM24QkM4kAIsPUKbwgLsK/1jM/p2yaP8OMXytiCdcJ0iIj6MjHNp0Q3XhDJEPtcuRRuzrojECgYEAia8n5/xTlhGzlhjrMmaKKdn3IxAYXhiuu+D9I5d21PoURI6DP8xUOW2ErDfHSzeKjlGRpJ5nPAX6ySpT4ifNaAGUiE6IoB8HKy6jy+443KmmCDIpPHseyqrelItYm4va8z20WhOKZSibpW5TPpBnDE1y55qfyAj9HENbJEnRT2UCgYEAxJhATzaX7KSd6FuHlE7byQTMSTSarPjVchAWH8exIIVFKO8SELP1OBibSd1Obuaeh/LxmTr+CPVGOWYAyu3KqT0D5QXEDnd0Wb16Rjfjudi4cogmay0MsxzM5SK76nV3G+HUvXaKZrtqg1zq5D4sPSRFbXhIZinTshmK6ysRxcU="
# 接口配置示例
aiqian_apis:
     idcard_verification_path: "/verify/person/idcard2"  # "个人身份证二要素比对"

# 商盟 统统付
sumpay:
     environment: "test"  # test, prod
     test_url: "https://test.sumpay.cn/entrance/gateway.htm"
     prod_url: "https://entrance.sumpay.cn/gateway.htm"
     mer_no: "s100000040"
     # 融担业务配置 - 资管和担保使用不同的商户号
     asset_mer_no: "s100000040"      # 资管业务商户号
     guarantee_mer_no: "200105383508" # 担保业务商户号
     app_id: "s100000040"
     domain: "www.baidu.com"
     notify_url: "http://www.baidu.com"
     skip_tls_verify: true      # 是否跳过TLS验证（测试环境建议true）
     request_timeout: 60        # 请求超时时间（秒）
     retry_count: 3             # 重试次数
     retry_wait_time: 5         # 重试等待时间（秒）
     cert:
          private_key_path: "resource/cert/yixuntiankong.pfx"
          public_key_path: "resource/cert/yixun.cer"
          password: "sumpay"


# 第三方风控配置
risk_thirdparty:
     # 测试环境
     merchant_id: "test123456test"           # 商户ID
     aes_key: "1111111111111111"             # AES加密密钥
     url: "http://*************/test/risk/interface"  # 第三方风控接口地址
     # 生产环境
     # merchant_id: "249ced55-0f8b-4e69-a0ba-d04d577a7e1b"           # 商户ID
     # aes_key: "66d8e4707819dc9f"             # AES加密密钥
     # url: "http://*************/api/risk/interface"  # 第三方风控接口地址
     product_id: "leida_v4"                  # 产品ID
     timeout: 45                             # 请求超时时间（秒）

# 风控模型服务配置
risk_model:
     base_url: "http://192.168.0.13:5000"  # 风控模型API地址
     timeout: 45                                 # 请求超时时间（秒）

# 第三方风控服务配置 (Third Party Risk Service)
third_risk:
     enabled: true                            # 是否启用第三方风控服务
     url: "http://riskopenapi.eenz.cn/"          # 第三方风控接口地址
     app_id: "KH202507241277780636309716992"     # 应用ID
     aes_key: "vIGit3Mxy1+ZA3/RiA8YpQ=="        # AES加密密钥
     timeout: 30                                 # 请求超时时间（秒）
     service_code: "risk_queryV3"                  # 服务码
     data_source: "third_party"                  # 数据源标识

# 风控评估配置
risk_evaluation:
     evaluation_expiry_hours: 24              # 评估结果有效期（小时）

# 系统代扣配置
system_deduct:
     max_fail_count: 15                        # 银行卡系统代扣最大失败次数 如果没有配置 则默认15次
