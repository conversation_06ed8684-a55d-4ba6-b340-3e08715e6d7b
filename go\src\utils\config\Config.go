package config

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

type Config struct {
	DBconf         DBconf         `yaml:"dbconf"`
	App            App            `yaml:"app"`
	Jwt            Jwt            `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Log            Log            `mapstructure:"log" json:"log" yaml:"log"`
	Sms            Sms            `mapstructure:"sms" json:"sms" yaml:"sms"`
	Vbank          Vbank          `mapstructure:"vbank" json:"vbank" yaml:"vbank"`
	Aiqian_envs    Aiqian_envs    `mapstructure:"aiqian_envs" json:"aiqian_envs" yaml:"aiqian_envs"`
	SumPay         SumPay         `mapstructure:"sumpay" json:"sumpay" yaml:"sumpay"`                            // 商盟 统统付
	RiskThirdParty RiskThirdParty `mapstructure:"risk_thirdparty" json:"risk_thirdparty" yaml:"risk_thirdparty"` // 第三方风控配置
	RiskModel      RiskModel      `mapstructure:"risk_model" json:"risk_model" yaml:"risk_model"`                // 风控模型服务配置
	ThirdRisk      ThirdRisk      `mapstructure:"third_risk" json:"third_risk" yaml:"third_risk"`                // 第三方风控服务配置
	RiskEvaluation RiskEvaluation `mapstructure:"risk_evaluation" json:"risk_evaluation" yaml:"risk_evaluation"` // 风控评估配置
	SystemDeduct   SystemDeduct   `mapstructure:"system_deduct" json:"system_deduct" yaml:"system_deduct"`       // 系统代扣配置
	Redis          Redis          `mapstructure:"redis" json:"redis" yaml:"redis"`                               // redis 配置
}

// 读取Yaml配置文件，并转换成Config对象  struct结构
func (config *Config) InitializeConfig() *Config {
	//获取项目的执行路径
	path, err := os.Getwd()
	if err != nil {
		panic(err)
	}
	// fmt.Println("path=", path)
	// resourcePath := filepath.Join(path, "../../resource") //这是dianziqian/main.go的路径
	resourcePath := filepath.Join(path, "resource")
	// resourcePath := filepath.Join(path, "../../../resource") //这是用来测试risk的路径
	vip := viper.New()
	vip.AddConfigPath(resourcePath) //设置读取的文件路径
	// 通过环境变量 ENV 来判断是否为开发环境
	if os.Getenv("ENV") == "dev" {
		vip.SetConfigName("config_dev")
	} else {
		vip.SetConfigName("config")
	}
	vip.SetConfigType("yml") //设置文件的类型
	log.Println("config.yml in ----------------- ", resourcePath)
	//尝试进行配置读取
	if err := vip.ReadInConfig(); err != nil {
		panic(err)
	}
	// 监听配置文件
	vip.WatchConfig()
	vip.OnConfigChange(func(in fsnotify.Event) {
		fmt.Println("config file changed:", in.Name)
		// 重载配置
		if err := vip.Unmarshal(&config); err != nil {
			fmt.Println(err)
		}
	})

	err = vip.Unmarshal(&config)
	if err != nil {
		panic(err)
	}
	return config
}
func listenSignal() {
	go func() {
		// 执行重启命令
		cmd := exec.Command("gofly", "run", "daemon", "restart")
		stdout, err := cmd.StdoutPipe()
		if err != nil {
			fmt.Println(err)
		}
		defer stdout.Close()

		if err := cmd.Start(); err != nil {
			panic(err)
		}
		reader := bufio.NewReader(stdout)
		//实时循环读取输出流中的一行内容
		for {
			line, err2 := reader.ReadString('\n')
			if err2 != nil || io.EOF == err2 {
				break
			}
			fmt.Print(line)
		}

		if err := cmd.Wait(); err != nil {
			fmt.Println(err)
		}
		opBytes, _ := io.ReadAll(stdout)
		fmt.Print(string(opBytes))

	}()
}
