package repayment

import (
	"errors"
	"fmt"
	"strings"
)

// PayOrderInfo 支付订单信息
type PayOrderInfo struct {
	OrderNo       string // 原始订单号
	TransactionNo string // 交易流水号
	WithholdType  string // 代扣类型：ASSET/GUARANTEE
}

// GeneratePayOrderNo 生成支付订单号
// 格式: {orderNo}_{transactionNo}_{withholdType}
// 例如: LO20241216ABCD1234_RP1703123456789000001_GUARANTEE
func GeneratePayOrderNo(orderNo, transactionNo, withholdType string) string {
	return fmt.Sprintf("%s_%s_%s", orderNo, transactionNo, withholdType)
}

// ParsePayOrderNo 解析支付订单号
// 从支付订单号中提取原始订单号、交易流水号和代扣类型
func ParsePayOrderNo(payOrderNo string) (*PayOrderInfo, error) {
	if payOrderNo == "" {
		return nil, errors.New("支付订单号不能为空")
	}

	// 按下划线分割
	parts := strings.Split(payOrderNo, "_")
	if len(parts) != 3 {
		return nil, fmt.Errorf("支付订单号格式错误，期望格式: orderNo_transactionNo_withholdType，实际: %s", payOrderNo)
	}

	orderNo := parts[0]
	transactionNo := parts[1]
	withholdType := parts[2]

	// 验证各部分是否为空
	if orderNo == "" {
		return nil, errors.New("订单号不能为空")
	}
	if transactionNo == "" {
		return nil, errors.New("交易流水号不能为空")
	}
	if withholdType == "" {
		return nil, errors.New("代扣类型不能为空")
	}

	// 验证代扣类型是否有效
	if withholdType != "ASSET" && withholdType != "GUARANTEE" {
		return nil, fmt.Errorf("无效的代扣类型: %s，有效值: ASSET, GUARANTEE", withholdType)
	}

	return &PayOrderInfo{
		OrderNo:       orderNo,
		TransactionNo: transactionNo,
		WithholdType:  withholdType,
	}, nil
}

// ParsePayOrderNoFields 从支付订单号中解析出三个字段
// 返回值：orderNo, transactionNo, withholdType, error
func ParsePayOrderNoFields(payOrderNo string) (string, string, string, error) {
	info, err := ParsePayOrderNo(payOrderNo)
	if err != nil {
		return "", "", "", err
	}
	return info.OrderNo, info.TransactionNo, info.WithholdType, nil
}

// ValidatePayOrderNo 验证支付订单号格式是否正确
func ValidatePayOrderNo(payOrderNo string) error {
	_, err := ParsePayOrderNo(payOrderNo)
	return err
}
