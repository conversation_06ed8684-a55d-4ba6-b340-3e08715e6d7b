package bankcard

import (
	"context"
	"net/http"
	"reflect"
	"time"

	backcard "fincore/app/business/bankcard"
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// BankCardController 银行卡控制器
type BankCardController struct{}

func init() {
	// 注册路由到自动路由系统
	gf.Register(&BankCardController{}, reflect.TypeOf(BankCardController{}).PkgPath())
}

// PostBindInfo 绑定用户信息接口
func (bc *BankCardController) PostBindInfo(c *gin.Context) {
	// 1. 参数校验
	requestData := make(map[string]interface{})
	if err := c.ShouldBindJSON(&requestData); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}

	schema := backcard.GetUserInfoSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := backcard.NewBankCardService(ctx).BindUserInfo(ctx, userID, validationResult.Data)
	if err != nil {
		results.Failed(c, "绑定信息失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(c, "绑定信息成功！", nil, nil)
}

// PostBindBankCardSms 发送银行卡绑定短信验证码接口
func (bc *BankCardController) PostBindBankCardSms(c *gin.Context) {
	// 1. 参数校验
	requestData := make(map[string]interface{})
	if err := c.ShouldBindJSON(&requestData); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}

	schema := backcard.GetBankCardInfoSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := backcard.NewBankCardService(ctx).SendBankCardSms(ctx, userID, validationResult.Data)
	if err != nil {
		results.Failed(c, err.Error(), nil)
		return
	}

	// 4. 返回结果
	results.Success(c, "短信验证码已发送，请输入验证码完成绑卡", result, nil)
}

// PostBindBankCard 绑定银行卡接口
func (bc *BankCardController) PostBindBankCard(c *gin.Context) {
	// 1. 参数校验
	requestData := make(map[string]interface{})
	if err := c.ShouldBindJSON(&requestData); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}

	schema := backcard.GetBankCardBindSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := backcard.NewBankCardService(ctx).BindBankCard(ctx, userID, validationResult.Data)
	if err != nil {
		results.Failed(c, err.Error(), nil)
		return
	}

	// 4. 返回结果
	results.Success(c, "银行卡绑定成功", result, nil)
}

// GetBankCardList 查询用户银行卡列表接口
// 路由: GET /uniapp/bankcard/get_bank_card_list
func (bc *BankCardController) GetBankCardList(c *gin.Context) {
	// 1. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := backcard.NewBankCardService(ctx).GetBankCardList(ctx, userID)
	if err != nil {
		results.Failed(c, "查询银行卡列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "查询成功", result, nil)
}

// GetHealth 健康检查接口
// 路由: GET /uniapp/bankcard/get_health
func (bc *BankCardController) GetHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "fincore-uniapp-bankcard-service",
		"version": "1.0.0",
		"time":    time.Now().Format("2006-01-02 15:04:05"),
	})
}
