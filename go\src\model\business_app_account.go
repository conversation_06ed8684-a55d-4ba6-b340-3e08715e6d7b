package model

import (
	"context"
	"fincore/utils/gform"
	"fincore/utils/idcard"
	"fincore/utils/pagination"
	"fmt"
	"strconv"
	"time"
)

// 类型 正常 白名单 黑名单 枚举

const (
	StatusTypeNormal    = iota // 正常
	StatusTypeWhitelist        // 白名单
	StatusTypeBlacklist        // 黑名单
)

// BusinessAppAccount 业务应用账户数据模型 - 与数据库表结构完全一致，并包含关联字段
type BusinessAppAccount struct {
	ID                        int64      `json:"id" gorm:"primaryKey"`
	Username                  string     `json:"username"`
	Name                      string     `json:"name"`
	IDCardFrontUrl            string     `json:"idCardFrontUrl"`
	IDCardBackUrl             string     `json:"idCardBackUrl"`
	Reviewer                  int        `json:"reviewer"`
	IDCardExpired             *time.Time `json:"idCardExpired"`
	IDCard                    string     `json:"idCard"`
	AllQuota                  float64    `json:"allQuota"`
	ReminderQuota             float64    `json:"reminderQuota"`
	RepeatBuyNum              int        `json:"repeatBuyNum"`
	ModelPoint                float64    `json:"modelPoint"`
	EmergencyContact0Name     string     `json:"emergencyContact0Name"`
	EmergencyContact0Phone    string     `json:"emergencyContact0Phone"`
	EmergencyContact0Relation string     `json:"emergencyContact0Relation"`
	EmergencyContact1Name     string     `json:"emergencyContact1Name"`
	EmergencyContact1Phone    string     `json:"emergencyContact1Phone"`
	EmergencyContact1Relation string     `json:"emergencyContact1Relation"`
	Degree                    string     `json:"degree"`
	Marry                     string     `json:"marry"`
	Occupation                string     `json:"occupation"`
	YearRevenue               string     `json:"yearRevenue"`
	PurposeOfBorrowing        string     `json:"purposeOfBorrowing"`
	IdentityStatus            uint64     `json:"identityStatus"`
	IdentitySuccessTime       *time.Time `json:"identitySuccessTime"` // 实名认证成功时间
	BizID                     string     `json:"bizId"`
	FacePhotoUrl              string     `json:"facePhotoUrl"`
	Avatar                    string     `json:"avatar"`
	Tel                       string     `json:"tel"`
	Mobile                    string     `json:"mobile"`
	Email                     string     `json:"email"`
	LastLoginIP               string     `json:"lastLoginIp"`
	LastLoginTime             int64      `json:"lastLoginTime"`
	LoginStatus               int        `json:"loginStatus"`
	ValidTime                 int64      `json:"validtime"`
	CreateTime                int64      `json:"createtime"`
	UpdateTime                int64      `json:"updatetime"`
	Address                   string     `json:"address"`
	City                      string     `json:"city"`
	Remark                    string     `json:"remark"`
	Company                   string     `json:"company"`
	ChannelID                 int        `json:"channelId"`
	Province                  string     `json:"province"`
	FileSize                  int64      `json:"fileSize"`
	RiskFlowNumber            string     `json:"riskFlowNumber"`
	RiskScore                 float64    `json:"riskScore"`
	UserRemark                string     `json:"userRemark"`
	RemarkTime                int64      `json:"remarkTime"`
	ComplaintStatus           int        `json:"complaintStatus"`
	DeviceSource              string     `json:"deviceSource"`
	Status                    int        `json:"status"`
	IsCancelled               int        `json:"isCancelled"`
	Reserved1                 string     `json:"reserved1"`
	Reserved2                 string     `json:"reserved2"`
	Reserved3                 string     `json:"reserved3"`
	AvailableProductID        int        `json:"availableProductID"`

	// 关联字段 - 用于前端显示
	ChannelName  string `json:"channelName"`  // 渠道名称
	ReviewerName string `json:"reviewerName"` // 审核者名称
	Gender       string `json:"gender"`       // 性别
	Age          int    `json:"age"`          // 年龄
	OcrEndDate   string `json:"ocrEndDate"`   // 身份证到期日

	// 窗口函数查询字段 - 用于筛选条件
	OrderStatus int `json:"orderStatus"` // 订单状态（最新订单的状态）
	LoanCount   int `json:"loanCount"`   // 放款次数（状态为1或3的订单数量）
}

// RepurchaseBusinessAppAccount 复购业务应用账户数据模型
type RepurchaseBusinessAppAccount struct {
	ID                  int64   `json:"id" db:"id"`
	ChannelName         string  `json:"channelName" db:"channel_name"`
	IsCancelled         int     `json:"isCancelled" db:"isCancelled"`
	IsCancelledText     string  `json:"isCancelledText"`
	Name                string  `json:"name" db:"name"`
	Mobile              string  `json:"mobile" db:"mobile"`
	IdCardMasked        string  `json:"idCardMasked"`
	IdCardFull          string  `json:"idCardFull" db:"idCard"`
	TotalAmount         float64 `json:"totalAmount" db:"allQuota"`
	AvailableAmount     float64 `json:"availableAmount" db:"reminderQuota"`
	BorrowingOrderCount int     `json:"borrowingOrderCount" db:"borrowingOrderCount"`
	TotalOrderCount     int     `json:"totalOrderCount" db:"totalOrderCount"`
	RegisterTime        string  `json:"registerTime" db:"register_time"`
	LastRepayTime       string  `json:"lastRepayTime" db:"lastRepayTime"`
	BillDueTime         string  `json:"billDueTime" db:"billDueTime"`
	LastAwakenTime      string  `json:"lastAwakenTime" db:"awaken_time"`
	AwakenCount         int     `json:"awakenCount" db:"awaken_count"`
	AwakenRemark        string  `json:"awakenRemark" db:"awaken_remark"`
}

// RepurchaseAwakenRecord 复购唤醒记录数据模型
type RepurchaseAwakenRecord struct {
	ID            int64  `json:"id" db:"id"`
	CustomerID    int64  `json:"customerId" db:"customer_id"`
	AwakenType    int    `json:"awakenType" db:"awaken_type"`
	AwakenContent string `json:"awakenContent" db:"awaken_content"`
	AwakenStatus  int    `json:"awakenStatus" db:"awaken_status"`
	OperatorID    int64  `json:"operatorId" db:"operator_id"`
	OperatorName  string `json:"operatorName" db:"operator_name"`
	Remark        string `json:"remark" db:"remark"`
	CreateTime    int64  `json:"createTime" db:"create_time"`
}

// BusinessAppAccountOption 业务应用账户选项结构
type BusinessAppAccountOption struct {
	Value string `json:"value"`
	Label string `json:"label"`
}

// BusinessAppAccountOptions 业务应用账户筛选选项
// BusinessAppAccountService 业务应用账户服务
type BusinessAppAccountService struct {
	ctx context.Context
}

// NewBusinessAppAccountService 创建业务应用账户服务实例
func NewBusinessAppAccountService() *BusinessAppAccountService {
	return &BusinessAppAccountService{}
}

type BusinessAppAccountOptions struct {
	ReviewerOptions []BusinessAppAccountOption `json:"reviewerOptions"`
	ChannelOptions  []BusinessAppAccountOption `json:"channelOptions"`
}

// applyBusinessAppAccountFilters 应用业务应用账户筛选条件
func (s *BusinessAppAccountService) applyBusinessAppAccountFilters(query gform.IOrm, filters map[string]interface{}) gform.IOrm {
	// 基础信息查询条件
	if name, ok := filters["name"]; ok && name != "" {
		query = query.Where("baa.name", "like", fmt.Sprintf("%%%v%%", name))
	}

	if mobile, ok := filters["mobile"]; ok && mobile != "" {
		query = query.Where("baa.mobile", "like", fmt.Sprintf("%%%v%%", mobile))
	}

	if idCard, ok := filters["idCard"]; ok && idCard != "" {
		query = query.Where("baa.idCard", "like", fmt.Sprintf("%%%v%%", idCard))
	}

	if reviewerID, ok := filters["reviewerID"]; ok && reviewerID != "" {
		query = query.Where("baa.reviewer", reviewerID)
	}

	if channelId, ok := filters["channelId"]; ok && channelId != "" {
		query = query.Where("baa.channelId", channelId)
	}

	// 风控相关查询条件
	if riskFlowNumber, ok := filters["riskFlowNumber"]; ok && riskFlowNumber != "" {
		query = query.Where("baa.riskFlowNumber", "like", fmt.Sprintf("%%%v%%", riskFlowNumber))
	}

	if riskScoreMin, ok := filters["riskScoreMin"]; ok && riskScoreMin != "" {
		query = query.Where("baa.riskScore", ">=", riskScoreMin)
	}

	if riskScoreMax, ok := filters["riskScoreMax"]; ok && riskScoreMax != "" {
		query = query.Where("baa.riskScore", "<=", riskScoreMax)
	}

	// 额度查询条件
	if reminderQuotaMin, ok := filters["reminderQuotaMin"]; ok && reminderQuotaMin != "" {
		query = query.Where("baa.reminderQuota", ">=", reminderQuotaMin)
	}

	if reminderQuotaMax, ok := filters["reminderQuotaMax"]; ok && reminderQuotaMax != "" {
		query = query.Where("baa.reminderQuota", "<=", reminderQuotaMax)
	}

	// 状态查询条件
	if deviceSource, ok := filters["deviceSource"]; ok && deviceSource != "" {
		query = query.Where("baa.deviceSource", deviceSource)
	}

	if userRemark, ok := filters["userRemark"]; ok && userRemark != "" {
		query = query.Where("baa.userRemark", "like", fmt.Sprintf("%%%v%%", userRemark))
	}

	// 投诉状态查询条件
	if complaintStatus, ok := filters["complaintStatus"]; ok && complaintStatus != "" {
		query = query.Where("baa.complaintStatus", complaintStatus)
	}

	if registerTimeStart, ok := filters["registerTimeStart"]; ok && registerTimeStart != "" {
		query = query.Where("baa.createtime", ">=", fmt.Sprintf("UNIX_TIMESTAMP('%v')", registerTimeStart))
	}

	if registerTimeEnd, ok := filters["registerTimeEnd"]; ok && registerTimeEnd != "" {
		query = query.Where("baa.createtime", "<=", fmt.Sprintf("UNIX_TIMESTAMP('%v')", registerTimeEnd))
	}

	return query
}

// GetBusinessAppAccountList 获取业务应用账户列表（带分页）
func (s *BusinessAppAccountService) GetBusinessAppAccountList(filters map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := s.applyBusinessAppAccountFilters(
		DB().Table("business_app_account baa").
			LeftJoin("channel c", "baa.channelId = c.id").
			LeftJoin("business_account ba", "baa.reviewer = ba.id"),
		filters)

	dataQuery := s.applyBusinessAppAccountFilters(
		DB().Table("business_app_account baa").
			LeftJoin("channel c", "baa.channelId = c.id").
			LeftJoin("business_account ba", "baa.reviewer = ba.id"),
		filters).
		Fields(`baa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,
			baa.deviceSource, baa.userRemark, baa.createtime,
			baa.identityStatus, baa.complaintStatus,
			baa.allQuota, baa.reminderQuota, baa.riskScore,
			c.channel_name as channel_name,
			ba.name as reviewer_name, baa.idCardFrontUrl, baa.idCardBackUrl`).
		OrderBy("baa.createtime DESC")

	// 使用自定义分页查询，避免ORM对象污染
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, err
	}

	// 转换数据格式
	if rawData, ok := result.Data.([]gform.Data); ok {
		var businessAppAccounts []BusinessAppAccount
		for _, item := range rawData {
			businessAppAccount := BusinessAppAccount{}
			businessAppAccount.ID = getInt64FromMap(item, "id")
			businessAppAccount.Name = getStringFromMap(item, "name")
			businessAppAccount.Mobile = getStringFromMap(item, "mobile")
			businessAppAccount.IDCard = getStringFromMap(item, "idCard")
			businessAppAccount.DeviceSource = getStringFromMap(item, "deviceSource")
			businessAppAccount.UserRemark = getStringFromMap(item, "userRemark")
			businessAppAccount.CreateTime = getInt64FromMap(item, "createtime")
			businessAppAccount.IdentityStatus = getUint64FromMap(item, "identityStatus")
			businessAppAccount.ComplaintStatus = getIntFromMap(item, "complaintStatus")
			businessAppAccount.AllQuota = getFloat64FromMap(item, "allQuota")
			businessAppAccount.ReminderQuota = getFloat64FromMap(item, "reminderQuota")
			businessAppAccount.RiskScore = getFloat64FromMap(item, "riskScore")
			businessAppAccount.ChannelID = getIntFromMap(item, "channelId")
			businessAppAccount.ChannelName = getStringFromMap(item, "channel_name")
			businessAppAccount.ReviewerName = getStringFromMap(item, "reviewer_name")
			businessAppAccount.IDCardFrontUrl = getStringFromMap(item, "idCardFrontUrl")
			businessAppAccount.IDCardBackUrl = getStringFromMap(item, "idCardBackUrl")
			businessAppAccounts = append(businessAppAccounts, businessAppAccount)
		}
		result.Data = businessAppAccounts
	}

	return result, nil
}

// applyRepurchaseFilters 应用复购业务应用账户筛选条件
func (s *BusinessAppAccountService) applyRepurchaseFilters(query gform.IOrm, filters map[string]interface{}) gform.IOrm {
	// 基础信息筛选
	if name, ok := filters["name"]; ok && name != "" {
		query = query.Where("baa.name", "like", fmt.Sprintf("%%%s%%", name))
	}
	if mobile, ok := filters["mobile"]; ok && mobile != "" {
		query = query.Where("baa.mobile", "like", fmt.Sprintf("%%%s%%", mobile))
	}
	if idCard, ok := filters["idCard"]; ok && idCard != "" {
		query = query.Where("baa.idCard", "like", fmt.Sprintf("%%%s%%", idCard))
	}

	// 渠道筛选
	if channelId, ok := filters["channelId"]; ok && channelId != "" {
		query = query.Where("baa.channelId", channelId)
	}

	// 注销状态筛选
	if isCancelled, ok := filters["isCancelled"]; ok && isCancelled != "" {
		query = query.Where("baa.isCancelled", isCancelled)
	}

	// 订单数筛选
	if borrowingOrderCount, ok := filters["borrowingOrderCount"]; ok && borrowingOrderCount != "" {
		query = query.Where("baa.borrowingOrderCount", borrowingOrderCount)
	}
	if totalOrderCount, ok := filters["totalOrderCount"]; ok && totalOrderCount != "" {
		query = query.Where("baa.totalOrderCount", totalOrderCount)
	}

	// 额度范围筛选
	if totalAmountMin, ok := filters["totalAmountMin"]; ok {
		if val, ok := totalAmountMin.(float64); ok && val > 0 {
			query = query.Where("baa.allQuota", ">=", val)
		}
	}
	if totalAmountMax, ok := filters["totalAmountMax"]; ok {
		if val, ok := totalAmountMax.(float64); ok && val > 0 {
			query = query.Where("baa.allQuota", "<=", val)
		}
	}
	if availableAmountMin, ok := filters["availableAmountMin"]; ok {
		if val, ok := availableAmountMin.(float64); ok && val > 0 {
			query = query.Where("baa.reminderQuota", ">=", val)
		}
	}
	if availableAmountMax, ok := filters["availableAmountMax"]; ok {
		if val, ok := availableAmountMax.(float64); ok && val > 0 {
			query = query.Where("baa.reminderQuota", "<=", val)
		}
	}

	// 时间范围筛选
	if lastRepayTimeStart, ok := filters["lastRepayTimeStart"]; ok && lastRepayTimeStart != "" {
		query = query.Where("baa.lastRepayTime", ">=", fmt.Sprintf("UNIX_TIMESTAMP('%s')", lastRepayTimeStart))
	}
	if lastRepayTimeEnd, ok := filters["lastRepayTimeEnd"]; ok && lastRepayTimeEnd != "" {
		query = query.Where("baa.lastRepayTime", "<=", fmt.Sprintf("UNIX_TIMESTAMP('%s')", lastRepayTimeEnd))
	}
	if billDueTimeStart, ok := filters["billDueTimeStart"]; ok && billDueTimeStart != "" {
		query = query.Where("baa.billDueTime", ">=", fmt.Sprintf("UNIX_TIMESTAMP('%s')", billDueTimeStart))
	}
	if billDueTimeEnd, ok := filters["billDueTimeEnd"]; ok && billDueTimeEnd != "" {
		query = query.Where("baa.billDueTime", "<=", fmt.Sprintf("UNIX_TIMESTAMP('%s')", billDueTimeEnd))
	}

	return query
}

// GetUserByID 根据ID获取用户信息
func GetUserByID(id int64) (map[string]interface{}, error) {
	return DB().Table("business_app_account").Where("id", id).First()
}

// applyRepurchaseSort 应用复购业务应用账户排序
func (s *BusinessAppAccountService) applyRepurchaseSort(query gform.IOrm, sortType string) gform.IOrm {
	switch sortType {
	case "available_amount_desc":
		query = query.OrderBy("baa.reminderQuota DESC")
	case "available_amount_asc":
		query = query.OrderBy("baa.reminderQuota ASC")
	case "last_repay_time_desc":
		query = query.OrderBy("baa.lastRepayTime DESC")
	case "last_repay_time_asc":
		query = query.OrderBy("baa.lastRepayTime ASC")
	default:
		// 默认按最后还款时间倒序
		query = query.OrderBy("baa.lastRepayTime DESC")
	}
	return query
}

// GetRepurchaseBusinessAppAccountList 获取复购业务应用账户列表（带分页）
func (s *BusinessAppAccountService) GetRepurchaseBusinessAppAccountList(filters map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := s.applyRepurchaseFilters(
		DB().Table("business_app_account baa").
			LeftJoin("channel c", "baa.channelId = c.id").
			Where("baa.reminderQuota > 0").
			Where("baa.totalOrderCount > 0").
			Where("baa.status", 0),
		filters)

	dataQuery := s.applyRepurchaseFilters(
		DB().Table("business_app_account baa").
			LeftJoin("channel c", "baa.channelId = c.id").
			LeftJoin(`(
				SELECT 
					customer_id,
					remark,
					create_time,
					ROW_NUMBER() OVER (PARTITION BY customer_id ORDER BY create_time DESC) as rn
				FROM repurchase_awaken_record
			) latest_awaken`, "latest_awaken.customer_id = baa.id AND latest_awaken.rn = 1").
			Where("baa.reminderQuota > 0").
			Where("baa.totalOrderCount > 0").
			Where("baa.status", 0),
		filters).
		Fields(`baa.id, 
			COALESCE(c.channel_name, '') as channel_name,
			baa.isCancelled,
			baa.name,
			baa.mobile,
			baa.idCard,
			baa.allQuota,
			baa.reminderQuota,
			baa.borrowingOrderCount,
			baa.totalOrderCount,
			baa.createtime as register_time,
			baa.lastRepayTime,
			baa.billDueTime,
			latest_awaken.create_time as awaken_time,
			latest_awaken.remark as awaken_remark`)

	// 应用排序
	if sortType, ok := filters["sortType"]; ok && sortType != "" {
		dataQuery = s.applyRepurchaseSort(dataQuery, sortType.(string))
	} else {
		dataQuery = s.applyRepurchaseSort(dataQuery, "")
	}

	// 使用自定义分页查询，避免ORM对象污染
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, err
	}

	// 转换数据格式
	if rawData, ok := result.Data.([]gform.Data); ok {
		var businessAppAccounts []RepurchaseBusinessAppAccount
		for _, item := range rawData {
			businessAppAccount := RepurchaseBusinessAppAccount{}
			// 手动映射数据
			businessAppAccount.ID = getInt64FromMap(item, "id")
			businessAppAccount.ChannelName = getStringFromMap(item, "channel_name")
			businessAppAccount.IsCancelled = getIntFromMap(item, "isCancelled")
			businessAppAccount.Name = getStringFromMap(item, "name")
			businessAppAccount.Mobile = getStringFromMap(item, "mobile")
			businessAppAccount.IdCardFull = getStringFromMap(item, "idCard")
			businessAppAccount.TotalAmount = getFloat64FromMap(item, "allQuota")
			businessAppAccount.AvailableAmount = getFloat64FromMap(item, "reminderQuota")
			businessAppAccount.BorrowingOrderCount = getIntFromMap(item, "borrowingOrderCount")
			businessAppAccount.TotalOrderCount = getIntFromMap(item, "totalOrderCount")
			businessAppAccount.AwakenRemark = getStringFromMap(item, "awaken_remark")

			// 处理脱敏和状态文本
			businessAppAccount.IdCardMasked = s.maskIdCard(businessAppAccount.IdCardFull)
			businessAppAccount.IsCancelledText = s.getCancelledText(businessAppAccount.IsCancelled)
			businessAppAccount.RegisterTime = s.formatTimestamp(getInt64FromMap(item, "register_time"))
			businessAppAccount.LastRepayTime = s.formatTimestamp(getInt64FromMap(item, "lastRepayTime"))
			businessAppAccount.BillDueTime = s.formatTimestamp(getInt64FromMap(item, "billDueTime"))
			businessAppAccount.LastAwakenTime = s.formatTimestamp(getInt64FromMap(item, "awaken_time"))
			businessAppAccounts = append(businessAppAccounts, businessAppAccount)
		}
		result.Data = businessAppAccounts
	}

	return result, nil
}

// GetBusinessAppAccountByID 根据ID获取业务应用账户详情（包含最新订单状态）
func (s *BusinessAppAccountService) GetBusinessAppAccountByID(id int64) (*BusinessAppAccount, error) {
	// 使用窗口函数查询，获取用户的最新订单状态信息
	data, err := DB().Table("business_app_account baa").
		LeftJoin("channel c", "baa.channelId = c.id").
		LeftJoin(`(
			SELECT
				user_id,
				FIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,
				SUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,
				ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
			FROM business_loan_orders
		) order_filter`, "baa.id = order_filter.user_id AND order_filter.rn = 1").
		Fields(`baa.id, baa.name, baa.mobile, baa.idCardFrontUrl, baa.idCardBackUrl, baa.idCard, baa.channelId,
			baa.deviceSource, baa.userRemark, baa.createtime,
			baa.emergencyContact0Name, baa.emergencyContact0Phone, baa.emergencyContact0Relation, baa.emergencyContact1Name, baa.emergencyContact1Phone, baa.emergencyContact1Relation,
			baa.degree, baa.marry, baa.occupation, baa.yearRevenue,
			baa.identityStatus, baa.complaintStatus,
			baa.allQuota, baa.reminderQuota, baa.riskScore, baa.facePhotoUrl, baa.availableProductID,
			COALESCE(c.channel_name, '') as channel_name,
			COALESCE(order_filter.orderStatus, 0) as orderStatus,
			COALESCE(order_filter.loanCount, 0) as loanCount`).
		Where("baa.id", id).
		First()

	if err != nil {
		return nil, fmt.Errorf("查询业务应用账户失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("业务应用账户不存在")
	}

	businessAppAccount := BusinessAppAccount{}
	// 手动映射数据
	businessAppAccount.ID = getInt64FromMap(data, "id")
	businessAppAccount.Name = getStringFromMap(data, "name")
	businessAppAccount.Mobile = getStringFromMap(data, "mobile")
	businessAppAccount.IDCardFrontUrl = getStringFromMap(data, "idCardFrontUrl")
	businessAppAccount.IDCardBackUrl = getStringFromMap(data, "idCardBackUrl")
	businessAppAccount.IDCard = getStringFromMap(data, "idCard")
	businessAppAccount.DeviceSource = getStringFromMap(data, "deviceSource")
	businessAppAccount.UserRemark = getStringFromMap(data, "userRemark")
	businessAppAccount.CreateTime = getInt64FromMap(data, "createtime")
	businessAppAccount.IdentityStatus = getUint64FromMap(data, "identityStatus")
	businessAppAccount.ComplaintStatus = getIntFromMap(data, "complaintStatus")
	businessAppAccount.AllQuota = getFloat64FromMap(data, "allQuota")
	businessAppAccount.ReminderQuota = getFloat64FromMap(data, "reminderQuota")
	businessAppAccount.RiskScore = getFloat64FromMap(data, "riskScore")
	businessAppAccount.ChannelID = getIntFromMap(data, "channelId")
	businessAppAccount.AvailableProductID = getIntFromMap(data, "availableProductID")
	// 关联字段映射
	businessAppAccount.ChannelName = getStringFromMap(data, "channel_name")
	businessAppAccount.Gender = idcard.GetGenderFromIDCard(businessAppAccount.IDCard)
	businessAppAccount.Age = idcard.CalculateAge(businessAppAccount.IDCard)
	businessAppAccount.EmergencyContact0Name = getStringFromMap(data, "emergencyContact0Name")
	businessAppAccount.EmergencyContact0Phone = getStringFromMap(data, "emergencyContact0Phone")
	businessAppAccount.EmergencyContact0Relation = getStringFromMap(data, "emergencyContact0Relation")
	businessAppAccount.EmergencyContact1Name = getStringFromMap(data, "emergencyContact1Name")
	businessAppAccount.EmergencyContact1Phone = getStringFromMap(data, "emergencyContact1Phone")
	businessAppAccount.EmergencyContact1Relation = getStringFromMap(data, "emergencyContact1Relation")
	businessAppAccount.Degree = getStringFromMap(data, "degree")
	businessAppAccount.Marry = getStringFromMap(data, "marry")
	businessAppAccount.Occupation = getStringFromMap(data, "occupation")
	businessAppAccount.YearRevenue = getStringFromMap(data, "yearRevenue")
	businessAppAccount.OcrEndDate = s.GetOcrRecordEndDateByUserId(businessAppAccount.ID)
	businessAppAccount.FacePhotoUrl = getStringFromMap(data, "facePhotoUrl")
	businessAppAccount.OrderStatus = getIntFromMap(data, "orderStatus") // 最新订单状态
	businessAppAccount.LoanCount = getIntFromMap(data, "loanCount")     // 放款次数

	return &businessAppAccount, nil
}

// GetReminderQuotaByUserID 根据用户ID获取剩余额度
func (s *BusinessAppAccountService) GetReminderQuotaByUserID(userID int64) (float64, error) {
	if userID <= 0 {
		return 0, fmt.Errorf("用户ID无效")
	}

	data, err := DB().Table("business_app_account").
		Where("id", userID).
		Fields("reminderQuota").
		First()

	if err != nil {
		return 0, fmt.Errorf("查询用户剩余额度失败: %v", err)
	}

	if len(data) == 0 {
		return 0, fmt.Errorf("用户账户不存在")
	}

	reminderQuota := getFloat64FromMap(data, "reminderQuota")
	return reminderQuota, nil
}

// 获取OCR识别结果的到期日字符串
func (s *BusinessAppAccountService) GetOcrRecordEndDateByUserId(userId int64) string {
	ocrIdentityRecordService := NewOCRIdentityRecordService()
	ocrRecord, _ := ocrIdentityRecordService.GetOCRRecordByUserID(uint32(userId))
	if ocrRecord == nil || ocrRecord.EndDate.IsZero() {
		return ""
	}
	return ocrRecord.EndDate.Format("2006-01-02")
}

// UpdateBusinessAppAccountStatus 更新业务应用账户状态
func (s *BusinessAppAccountService) UpdateBusinessAppAccountStatus(id int64, data map[string]interface{}) error {
	_, err := DB().Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新业务应用账户状态失败: %v", err)
	}
	return nil
}

// UpdateBusinessAppAccountRemark 更新业务应用账户备注
func (s *BusinessAppAccountService) UpdateBusinessAppAccountRemark(id int64, data map[string]interface{}) error {
	_, err := DB().Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新业务应用账户备注失败: %v", err)
	}
	return nil
}

// ======================== 支持事务的额度更新方法 ========================
// 事务更新剩余额度
func (s *BusinessAppAccountService) UpdateReminderQuotaWithTx(db gform.IOrm, id int64, reminderQuota float64) error {
	data := map[string]interface{}{
		"reminderQuota": reminderQuota,
	}
	_, err := db.Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新剩余额度失败: %v", err)
	}
	return nil
}

// UpdateQuotaAndRiskScoreWithTx使用事务同时更新总额度和剩余额度和风控分数
func (s *BusinessAppAccountService) UpdateQuotaAndRiskScoreWithTx(db gform.IOrm, id int64, allQuota, reminderQuota float64, riskScore int) error {
	data := map[string]interface{}{
		"allQuota":      allQuota,
		"reminderQuota": reminderQuota,
		"riskScore":     riskScore,
	}
	_, err := db.Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新额度和风控分数失败: %v", err)
	}
	return nil
}

// 使用事务更新用户的可用产品id
func (s *BusinessAppAccountService) UpdateAvailableProductIDWithTx(db gform.IOrm, id int64, availableProductID int) error {
	data := map[string]interface{}{
		"availableProductID": availableProductID,
	}
	_, err := db.Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新可用产品id失败: %v", err)
	}
	return nil
}

// 更新总额度和剩余额度
func (s *BusinessAppAccountService) UpdateQuota(id int64, allQuota, reminderQuota float64) error {
	data := map[string]interface{}{
		"allQuota":      allQuota,
		"reminderQuota": reminderQuota,
	}
	_, err := DB().Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新额度失败: %v", err)
	}
	return nil
}

// UpdateRiskScoreWithTx 使用事务更新用户的风控分数
func (s *BusinessAppAccountService) UpdateRiskScoreWithTx(db gform.IOrm, id int64, riskScore int) error {
	data := map[string]interface{}{
		"riskScore": riskScore,
	}
	_, err := db.Table("business_app_account").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新 riskScore 失败: %v", err)
	}
	return nil
}

// GetBusinessAppAccountOptions 获取业务应用账户筛选选项
func (s *BusinessAppAccountService) GetBusinessAppAccountOptions() (*BusinessAppAccountOptions, error) {
	// 获取审核员选项
	reviewerData, err := DB().Table("business_account").
		Fields("id, name").
		Where("status", 0).
		Get()
	if err != nil {
		return nil, fmt.Errorf("获取审核员选项失败: %v", err)
	}

	reviewerOptions := make([]BusinessAppAccountOption, 0)
	for _, item := range reviewerData {
		reviewerOptions = append(reviewerOptions, BusinessAppAccountOption{
			Value: fmt.Sprintf("%v", item["id"]),
			Label: fmt.Sprintf("%v", item["name"]),
		})
	}

	// 获取渠道选项
	channelData, err := DB().Table("channel").
		Fields("id, channel_name").
		Where("channel_status", 1).
		Get()
	if err != nil {
		return nil, fmt.Errorf("获取渠道选项失败: %v", err)
	}

	channelOptions := make([]BusinessAppAccountOption, 0)
	for _, item := range channelData {
		channelOptions = append(channelOptions, BusinessAppAccountOption{
			Value: fmt.Sprintf("%v", item["id"]),
			Label: fmt.Sprintf("%v", item["channel_name"]),
		})
	}

	return &BusinessAppAccountOptions{
		ReviewerOptions: reviewerOptions,
		ChannelOptions:  channelOptions,
	}, nil
}

// UpdateQuotaAndProduct 更新客户额度和产品ID
func (s *BusinessAppAccountService) UpdateQuotaAndProduct(customerID int64, allQuota float64, reminderQuota float64, productID int) error {
	data := map[string]interface{}{
		"allQuota":           allQuota,
		"reminderQuota":      reminderQuota,
		"availableProductID": productID,
	}
	_, err := DB().Table("business_app_account").Where("id", customerID).Update(data)
	if err != nil {
		return fmt.Errorf("更新客户额度和产品ID失败: %v", err)
	}
	return nil
}

// ======================== 复购唤醒记录相关 ========================

// CreateRepurchaseAwakenRecord 创建复购唤醒记录
func (s *BusinessAppAccountService) CreateRepurchaseAwakenRecord(record RepurchaseAwakenRecord) error {
	currentTime := time.Now().Unix()

	insertData := map[string]interface{}{
		"customer_id":    record.CustomerID,
		"awaken_type":    record.AwakenType,
		"awaken_content": record.AwakenContent,
		"awaken_status":  1, // 1-已发送/已记录
		"operator_id":    record.OperatorID,
		"remark":         record.Remark,
		"create_time":    currentTime,
	}

	_, err := DB().Table("repurchase_awaken_record").Insert(insertData)
	if err != nil {
		return fmt.Errorf("创建复购唤醒记录失败: %v", err)
	}

	return nil
}

// GetRepurchaseAwakenRecords 获取复购唤醒记录列表
func (s *BusinessAppAccountService) GetRepurchaseAwakenRecords(customerID int64) ([]RepurchaseAwakenRecord, error) {
	data, err := DB().Table("repurchase_awaken_record r").
		LeftJoin("business_account ba", "r.operator_id", "ba.id").
		Fields("r.*, ba.name as operator_name").
		Where("r.customer_id", customerID).
		OrderBy("r.create_time DESC").
		Get()

	if err != nil {
		return nil, fmt.Errorf("获取复购唤醒记录失败: %v", err)
	}

	// 初始化空切片而不是nil，确保JSON序列化为[]而不是null
	records := make([]RepurchaseAwakenRecord, 0)

	for _, item := range data {
		operatorName := getStringFromMap(item, "operator_name")
		// 如果操作员名称为空，设置默认值
		if operatorName == "" {
			operatorName = "系统"
		}

		record := RepurchaseAwakenRecord{
			ID:            getInt64FromMap(item, "id"),
			CustomerID:    getInt64FromMap(item, "customer_id"),
			AwakenContent: getStringFromMap(item, "awaken_content"),
			AwakenType:    getIntFromMap(item, "awaken_type"),
			AwakenStatus:  getIntFromMap(item, "awaken_status"),
			OperatorID:    getInt64FromMap(item, "operator_id"),
			OperatorName:  operatorName,
			Remark:        getStringFromMap(item, "remark"),
			CreateTime:    getInt64FromMap(item, "create_time"),
		}
		records = append(records, record)
	}

	return records, nil
}

// SendRepurchaseSMS 发送复购短信（验证业务应用账户存在）
func (s *BusinessAppAccountService) SendRepurchaseSMS(customerID int64) error {
	// 验证业务应用账户存在性
	customerData, err := DB().Table("business_app_account").
		Where("id", customerID).
		Where("status", 0).
		First()
	if err != nil {
		return fmt.Errorf("业务应用账户不存在或已注销")
	}

	mobile := getStringFromMap(customerData, "mobile")
	if mobile == "" {
		return fmt.Errorf("业务应用账户手机号为空")
	}

	// TODO: 集成实际的短信发送服务
	return nil
}

// ======================== 默认数据获取方法 ========================

// GetDefaultChannelID 获取默认渠道ID（fincore）
func (s *BusinessAppAccountService) GetDefaultChannelID() int64 {
	data, err := DB().Table("channel").
		Fields("id").
		Where("channel_name", "fincore").
		Where("channel_status", 1).
		First()

	if err != nil || len(data) == 0 {
		// 如果fincore渠道不存在，返回第一个可用渠道
		data, err = DB().Table("channel").
			Fields("id").
			Where("channel_status", 1).
			OrderBy("id ASC").
			First()
		if err != nil || len(data) == 0 {
			return 0 // 没有可用渠道
		}
	}

	return getInt64FromMap(data, "id")
}

// GetDefaultReviewerID 获取默认审核员ID（超级管理员）
func (s *BusinessAppAccountService) GetDefaultReviewerID() int64 {
	data, err := DB().Table("business_account").
		Fields("id").
		Where("name", "超级管理员").
		Where("status", 0).
		First()

	if err != nil || len(data) == 0 {
		// 如果超级管理员不存在，返回第一个可用业务账户
		data, err = DB().Table("business_account").
			Fields("id").
			Where("status", 0).
			OrderBy("id ASC").
			First()
		if err != nil || len(data) == 0 {
			return 0 // 没有可用审核员
		}
	}

	return getInt64FromMap(data, "id")
}

// CreateBusinessAppAccountWithDefaults 创建业务应用账户时使用默认值
func (s *BusinessAppAccountService) CreateBusinessAppAccountWithDefaults(businessAppAccountData map[string]interface{}) error {
	// 设置默认渠道ID
	if _, exists := businessAppAccountData["channelId"]; !exists || businessAppAccountData["channelId"] == nil || businessAppAccountData["channelId"] == 0 {
		businessAppAccountData["channelId"] = 0
	}

	// 设置默认审核员ID
	if _, exists := businessAppAccountData["reviewer"]; !exists || businessAppAccountData["reviewer"] == nil || businessAppAccountData["reviewer"] == 0 {
		defaultReviewerID := s.GetDefaultReviewerID()
		if defaultReviewerID > 0 {
			businessAppAccountData["reviewer"] = defaultReviewerID
		}
	}

	// 设置默认状态
	if _, exists := businessAppAccountData["status"]; !exists {
		businessAppAccountData["status"] = 0 // 正常状态
	}

	// 处理身份证字段，避免唯一索引冲突
	// 如果没有设置idCard或者idCard为空字符串，则设置为NULL
	if idCard, exists := businessAppAccountData["idCard"]; !exists || idCard == "" {
		businessAppAccountData["idCard"] = nil
	}

	_, err := DB().Table("business_app_account").Insert(businessAppAccountData)
	if err != nil {
		return fmt.Errorf("创建业务应用账户失败: %v", err)
	}

	return nil
}

// ======================== 工具方法 ========================

// maskIdCard 脱敏身份证号
func (s *BusinessAppAccountService) maskIdCard(idCard string) string {
	if len(idCard) < 8 {
		return idCard
	}
	return idCard[:4] + "**********" + idCard[len(idCard)-4:]
}

// getCancelledText 获取注销状态文本
func (s *BusinessAppAccountService) getCancelledText(status int) string {
	if status == 1 {
		return "已注销"
	}
	return "正常"
}

// getIdentityStatusText 获取认证状态文本
func (s *BusinessAppAccountService) getIdentityStatusText(status int) string {
	switch status {
	case 0:
		return "未认证"
	case 1:
		return "已认证"
	default:
		return "未知"
	}
}

// getOrderStatusText 获取订单状态文本
func (s *BusinessAppAccountService) getOrderStatusText(status int) string {
	switch status {
	case 0:
		return "未下单"
	case 1:
		return "进行中"
	case 2:
		return "已完成"
	case 3:
		return "已取消"
	default:
		return "未知"
	}
}

// getComplaintStatusText 获取投诉状态文本
func (s *BusinessAppAccountService) getComplaintStatusText(status int) string {
	switch status {
	case 0:
		return "无投诉"
	case 1:
		return "有投诉"
	default:
		return "未知"
	}
}

// formatTimestamp 格式化时间戳
func (s *BusinessAppAccountService) formatTimestamp(timestamp int64) string {
	if timestamp == 0 {
		return ""
	}
	return time.Unix(timestamp, 0).Format("2006-01-02 15:04:05")
}

// 工具函数
func getStringFromMap(data map[string]interface{}, key string) string {
	if val, ok := data[key]; ok && val != nil {
		return fmt.Sprintf("%v", val)
	}
	return ""
}

func getIntFromMap(data map[string]interface{}, key string) int {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case int:
			return v
		case int64:
			return int(v)
		case float64:
			return int(v)
		case string:
			if intVal, err := strconv.Atoi(v); err == nil {
				return intVal
			}
		}
	}
	return 0
}

func getInt64FromMap(data map[string]interface{}, key string) int64 {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case int64:
			return v
		case int:
			return int64(v)
		case float64:
			return int64(v)
		case string:
			if int64Val, err := strconv.ParseInt(v, 10, 64); err == nil {
				return int64Val
			}
		}
	}
	return 0
}

func getFloat64FromMap(data map[string]interface{}, key string) float64 {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case float64:
			return v
		case int:
			return float64(v)
		case int64:
			return float64(v)
		case string:
			if floatVal, err := strconv.ParseFloat(v, 64); err == nil {
				return floatVal
			}
		}
	}
	return 0.0
}

func getUint64FromMap(data map[string]interface{}, key string) uint64 {
	if val, ok := data[key]; ok && val != nil {
		switch v := val.(type) {
		case uint64:
			return v
		case int64:
			return uint64(v)
		case int:
			return uint64(v)
		case float64:
			return uint64(v)
		case string:
			if uint64Val, err := strconv.ParseUint(v, 10, 64); err == nil {
				return uint64Val
			}
		}
	}
	return 0
}

// UnlockBusinessAppAccount 解除注销业务应用账户
func (s *BusinessAppAccountService) UnlockBusinessAppAccount(customerId int64) error {
	if customerId <= 0 {
		return fmt.Errorf("业务应用账户ID不能为空")
	}

	// 查询业务应用账户是否存在
	customer, err := DB().Table("business_app_account").
		Where("id", customerId).
		First()
	if err != nil {
		return fmt.Errorf("查询业务应用账户信息失败: %v", err)
	}
	if len(customer) == 0 {
		return fmt.Errorf("业务应用账户不存在")
	}

	// 更新业务应用账户状态，将isCancelled设置为0（正常状态）
	_, err = DB().Table("business_app_account").
		Where("id", customerId).
		Update(map[string]interface{}{
			"isCancelled": 0,
		})
	if err != nil {
		return fmt.Errorf("解除注销失败: %v", err)
	}

	return nil
}

// CheckChannelHasBusinessAppAccounts 检查渠道是否有关联的业务应用账户
func (s *BusinessAppAccountService) CheckChannelHasBusinessAppAccounts(channelID int) (bool, error) {
	count, err := DB().Table("business_app_account").Where("channelId", channelID).Count()
	if err != nil {
		return false, fmt.Errorf("检查渠道关联业务应用账户失败: %v", err)
	}
	return count > 0, nil
}

// UpdateIdentityStatus 更新用户认证状态
func (s *BusinessAppAccountService) UpdateIdentityStatus(userID int64, identityStatus int) error {
	if userID <= 0 {
		return fmt.Errorf("用户ID不能为空")
	}

	// 验证认证状态值的有效性
	if identityStatus < 0 || identityStatus > 3 {
		return fmt.Errorf("无效的认证状态值: %d", identityStatus)
	}

	// 更新数据库认证状态
	_, err := DB().Table("business_app_account").Data(map[string]interface{}{
		"identityStatus":      identityStatus,
		"identitySuccessTime": time.Now(),
	}).Where("id", userID).Update()

	if err != nil {
		return fmt.Errorf("更新用户认证状态失败: %v", err)
	}

	return nil
}

// ======================== 新增：窗口函数查询方法 ========================

// OrderStatsInfo 订单统计信息结构
type OrderStatsInfo struct {
	UserID              int64 `json:"userId" db:"user_id"`
	BorrowingOrderCount int   `json:"borrowingOrderCount" db:"borrowing_order_count"` // 在借订单数（状态为0和1）
	TotalOrderCount     int   `json:"totalOrderCount" db:"total_order_count"`         // 历史订单总数
	LoanTime            int64 `json:"loanTime" db:"loan_time"`                        // 进件时间（最近一次创建订单时间）
}

// RepayStatsInfo 还款统计信息结构
type RepayStatsInfo struct {
	UserID        int64 `json:"userId" db:"user_id"`
	LastRepayTime int64 `json:"lastRepayTime" db:"last_repay_time"` // 最后还款时间
	BillDueTime   int64 `json:"billDueTime" db:"bill_due_time"`     // 账单到期时间
}

// GetBusinessAppAccountListWithWindowFunction 使用窗口函数获取业务应用账户列表（带筛选条件）
func (s *BusinessAppAccountService) GetBusinessAppAccountListWithWindowFunction(filters map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 构建主查询SQL，使用窗口函数获取筛选字段
	baseSQL := `
		SELECT
			baa.id, baa.name, baa.mobile, baa.idCard, baa.reviewer, baa.channelId,
			baa.deviceSource, baa.userRemark, baa.createtime,
			baa.identityStatus, baa.complaintStatus,
			baa.allQuota, baa.reminderQuota, baa.riskScore,
			COALESCE(c.channel_name, '') as channel_name,
			COALESCE(ba.name, '超级管理员') as reviewer_name,
			baa.idCardFrontUrl, baa.idCardBackUrl,
			COALESCE(order_filter.orderStatus, 0) as orderStatus,
			COALESCE(order_filter.loanCount, 0) as loanCount
		FROM business_app_account baa
		LEFT JOIN channel c ON baa.channelId = c.id
		LEFT JOIN business_account ba ON baa.reviewer = ba.id
		LEFT JOIN (
			SELECT
				user_id,
				FIRST_VALUE(status) OVER (PARTITION BY user_id ORDER BY created_at DESC) as orderStatus,
				SUM(CASE WHEN status IN (1, 3) THEN 1 ELSE 0 END) OVER (PARTITION BY user_id) as loanCount,
				ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
			FROM business_loan_orders
		) order_filter ON baa.id = order_filter.user_id AND order_filter.rn = 1
		WHERE 1=1`

	// 应用筛选条件
	whereConditions := []string{}
	args := []interface{}{}

	// 基础信息筛选
	if id, ok := filters["id"]; ok && id != "" {
		whereConditions = append(whereConditions, "baa.id = ?")
		args = append(args, id)
	}
	if name, ok := filters["name"]; ok && name != "" {
		whereConditions = append(whereConditions, "baa.name LIKE ?")
		args = append(args, fmt.Sprintf("%%%v%%", name))
	}
	if mobile, ok := filters["mobile"]; ok && mobile != "" {
		whereConditions = append(whereConditions, "baa.mobile LIKE ?")
		args = append(args, fmt.Sprintf("%%%v%%", mobile))
	}
	if idCard, ok := filters["idCard"]; ok && idCard != "" {
		whereConditions = append(whereConditions, "baa.idCard LIKE ?")
		args = append(args, fmt.Sprintf("%%%v%%", idCard))
	}
	if reviewerID, ok := filters["reviewerID"]; ok && reviewerID != "" {
		whereConditions = append(whereConditions, "baa.reviewer = ?")
		args = append(args, reviewerID)
	}
	if channelId, ok := filters["channelId"]; ok && channelId != "" {
		whereConditions = append(whereConditions, "baa.channelId = ?")
		args = append(args, channelId)
	}

	// 订单筛选条件：orderStatus 和 loanCount
	if orderStatus, ok := filters["orderStatus"]; ok && orderStatus != "" {
		whereConditions = append(whereConditions, "COALESCE(order_filter.orderStatus, 0) = ?")
		args = append(args, orderStatus)
	}

	// 筛选大于等于放款次数
	if loanCount, ok := filters["loanCount"]; ok && loanCount != "" {
		whereConditions = append(whereConditions, "COALESCE(order_filter.loanCount, 0) >= ?")
		args = append(args, loanCount)
	}

	// 风控相关筛选条件
	if riskFlowNumber, ok := filters["riskFlowNumber"]; ok && riskFlowNumber != "" {
		whereConditions = append(whereConditions, "baa.riskFlowNumber LIKE ?")
		args = append(args, fmt.Sprintf("%%%v%%", riskFlowNumber))
	}
	if riskScoreMin, ok := filters["riskScoreMin"]; ok && riskScoreMin != "" {
		whereConditions = append(whereConditions, "baa.riskScore >= ?")
		args = append(args, riskScoreMin)
	}
	if riskScoreMax, ok := filters["riskScoreMax"]; ok && riskScoreMax != "" {
		whereConditions = append(whereConditions, "baa.riskScore <= ?")
		args = append(args, riskScoreMax)
	}

	// 额度相关筛选条件
	if reminderQuotaMin, ok := filters["reminderQuotaMin"]; ok && reminderQuotaMin != "" {
		whereConditions = append(whereConditions, "baa.reminderQuota >= ?")
		args = append(args, reminderQuotaMin)
	}
	if reminderQuotaMax, ok := filters["reminderQuotaMax"]; ok && reminderQuotaMax != "" {
		whereConditions = append(whereConditions, "baa.reminderQuota <= ?")
		args = append(args, reminderQuotaMax)
	}

	// 其他现有筛选条件
	if deviceSource, ok := filters["deviceSource"]; ok && deviceSource != "" {
		whereConditions = append(whereConditions, "baa.deviceSource = ?")
		args = append(args, deviceSource)
	}
	if userRemark, ok := filters["userRemark"]; ok && userRemark != "" {
		whereConditions = append(whereConditions, "baa.userRemark LIKE ?")
		args = append(args, fmt.Sprintf("%%%v%%", userRemark))
	}
	if complaintStatus, ok := filters["complaintStatus"]; ok && complaintStatus != "" {
		whereConditions = append(whereConditions, "baa.complaintStatus = ?")
		args = append(args, complaintStatus)
	}

	// 认证状态筛选条件
	if identityStatus, ok := filters["identityStatus"]; ok && identityStatus != "" {
		whereConditions = append(whereConditions, "baa.identityStatus = ?")
		args = append(args, identityStatus)
	}

	// 时间范围筛选
	if registerTimeStart, ok := filters["registerTimeStart"]; ok && registerTimeStart != "" {
		whereConditions = append(whereConditions, "baa.createtime >= UNIX_TIMESTAMP(?)")
		args = append(args, registerTimeStart)
	}
	if registerTimeEnd, ok := filters["registerTimeEnd"]; ok && registerTimeEnd != "" {
		whereConditions = append(whereConditions, "baa.createtime <= UNIX_TIMESTAMP(?)")
		args = append(args, registerTimeEnd)
	}

	// 构建完整的WHERE子句
	if len(whereConditions) > 0 {
		baseSQL += " AND " + whereConditions[0]
		for i := 1; i < len(whereConditions); i++ {
			baseSQL += " AND " + whereConditions[i]
		}
	}

	// 添加排序
	baseSQL += " ORDER BY baa.createtime DESC"

	// 执行分页查询
	countSQL := fmt.Sprintf("SELECT COUNT(*) as total FROM (%s) as count_query", baseSQL)

	// 计算总数
	countResult, err := DB().Query(countSQL, args...)
	if err != nil {
		return nil, fmt.Errorf("查询总数失败: %v", err)
	}

	total := int64(0)
	if len(countResult) > 0 {
		total = getInt64FromMap(countResult[0], "total")
	}

	// 计算分页参数
	offset := (paginationReq.Page - 1) * paginationReq.PageSize
	dataSQL := baseSQL + fmt.Sprintf(" LIMIT %d OFFSET %d", paginationReq.PageSize, offset)

	// 执行数据查询
	dataResult, err := DB().Query(dataSQL, args...)
	if err != nil {
		return nil, fmt.Errorf("查询数据失败: %v", err)
	}

	// 转换数据格式
	var businessAppAccounts []BusinessAppAccount
	for _, item := range dataResult {
		businessAppAccount := BusinessAppAccount{}
		businessAppAccount.ID = getInt64FromMap(item, "id")
		businessAppAccount.Name = getStringFromMap(item, "name")
		businessAppAccount.Mobile = getStringFromMap(item, "mobile")
		businessAppAccount.IDCard = getStringFromMap(item, "idCard")
		businessAppAccount.DeviceSource = getStringFromMap(item, "deviceSource")
		businessAppAccount.UserRemark = getStringFromMap(item, "userRemark")
		businessAppAccount.CreateTime = getInt64FromMap(item, "createtime")
		businessAppAccount.IdentityStatus = getUint64FromMap(item, "identityStatus")
		businessAppAccount.ComplaintStatus = getIntFromMap(item, "complaintStatus")
		businessAppAccount.AllQuota = getFloat64FromMap(item, "allQuota")
		businessAppAccount.ReminderQuota = getFloat64FromMap(item, "reminderQuota")
		businessAppAccount.RiskScore = getFloat64FromMap(item, "riskScore")
		businessAppAccount.ChannelName = getStringFromMap(item, "channel_name")
		businessAppAccount.ReviewerName = getStringFromMap(item, "reviewer_name")
		businessAppAccount.IDCardFrontUrl = getStringFromMap(item, "idCardFrontUrl")
		businessAppAccount.IDCardBackUrl = getStringFromMap(item, "idCardBackUrl")
		businessAppAccount.OrderStatus = getIntFromMap(item, "orderStatus")
		businessAppAccount.LoanCount = getIntFromMap(item, "loanCount")
		businessAppAccounts = append(businessAppAccounts, businessAppAccount)
	}

	// 构建分页响应
	totalPages := int((total + int64(paginationReq.PageSize) - 1) / int64(paginationReq.PageSize))
	response := &pagination.PaginationResponse{
		Data:       businessAppAccounts,
		Total:      total,
		Page:       paginationReq.Page,
		PageSize:   paginationReq.PageSize,
		TotalPages: totalPages,
		HasNext:    paginationReq.Page < totalPages,
		HasPrev:    paginationReq.Page > 1,
	}

	return response, nil
}

// GetOrderStatsForUsers 获取多个用户的订单统计信息（并行查询用）
func (s *BusinessAppAccountService) GetOrderStatsForUsers(userIDs []int64) (map[int64]OrderStatsInfo, error) {
	if len(userIDs) == 0 {
		return make(map[int64]OrderStatsInfo), nil
	}

	// 转换userIDs为interface{}切片
	userIDsInterface := make([]interface{}, len(userIDs))
	for i, userID := range userIDs {
		userIDsInterface[i] = userID
	}

	// 使用ORM构建查询，避免手动拼接SQL
	query := DB().Table("business_loan_orders").
		Fields("user_id, SUM(CASE WHEN status IN (0, 1) THEN 1 ELSE 0 END) as borrowing_order_count, COUNT(*) as total_order_count, MAX(UNIX_TIMESTAMP(created_at)) as loan_time").
		WhereIn("user_id", userIDsInterface).
		GroupBy("user_id")

	result, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询订单统计信息失败: %v", err)
	}

	statsMap := make(map[int64]OrderStatsInfo)
	for _, item := range result {
		userID := getInt64FromMap(item, "user_id")
		stats := OrderStatsInfo{
			UserID:              userID,
			BorrowingOrderCount: getIntFromMap(item, "borrowing_order_count"),
			TotalOrderCount:     getIntFromMap(item, "total_order_count"),
			LoanTime:            getInt64FromMap(item, "loan_time"),
		}
		statsMap[userID] = stats
	}

	return statsMap, nil
}

// GetRepayStatsForUsers 获取多个用户的还款统计信息（并行查询用）
func (s *BusinessAppAccountService) GetRepayStatsForUsers(userIDs []int64) (map[int64]RepayStatsInfo, error) {
	if len(userIDs) == 0 {
		return make(map[int64]RepayStatsInfo), nil
	}

	// 转换userIDs为interface{}切片
	userIDsInterface := make([]interface{}, len(userIDs))
	for i, userID := range userIDs {
		userIDsInterface[i] = userID
	}

	// 使用ORM构建查询
	query := DB().Table("business_repayment_bills").
		Fields("user_id, MAX(CASE WHEN paid_at IS NOT NULL THEN UNIX_TIMESTAMP(paid_at) ELSE NULL END) as last_repay_time, MIN(CASE WHEN status = 0 THEN UNIX_TIMESTAMP(due_date) ELSE NULL END) as bill_due_time").
		WhereIn("user_id", userIDsInterface).
		GroupBy("user_id")

	result, err := query.Get()
	if err != nil {
		return nil, fmt.Errorf("查询还款统计信息失败: %v", err)
	}

	statsMap := make(map[int64]RepayStatsInfo)
	for _, item := range result {
		userID := getInt64FromMap(item, "user_id")
		stats := RepayStatsInfo{
			UserID:        userID,
			LastRepayTime: getInt64FromMap(item, "last_repay_time"),
			BillDueTime:   getInt64FromMap(item, "bill_due_time"),
		}
		statsMap[userID] = stats
	}

	return statsMap, nil
}

// ======================== 统一查询方法（可复用） ========================

// QueryOptions 查询选项
type QueryOptions struct {
	IncludeStats bool  // 是否包含统计字段（用于列表和导出）
	ForExport    bool  // 是否用于导出（无分页限制）
	SingleRecord bool  // 是否查询单条记录（用于详情）
	RecordID     int64 // 记录ID（用于详情查询）
}

// GetBusinessAppAccountUnified 统一的业务应用账户查询方法
func (s *BusinessAppAccountService) GetBusinessAppAccountUnified(
	filters map[string]interface{},
	paginationReq pagination.PaginationRequest,
	options QueryOptions,
) (*pagination.PaginationResponse, error) {

	if options.SingleRecord && options.RecordID > 0 {
		// 单条记录查询（详情）
		return s.getBusinessAppAccountDetail(options.RecordID)
	} else if options.IncludeStats {
		// 包含统计字段的查询（列表和导出）
		if options.ForExport {
			// 导出查询：无分页限制
			paginationReq.PageSize = 10000
		}
		return s.GetBusinessAppAccountListWithWindowFunction(filters, paginationReq)
	} else {
		// 基础查询（不包含统计字段）
		return s.GetBusinessAppAccountList(filters, paginationReq)
	}
}

// getBusinessAppAccountDetail 获取单条记录详情
func (s *BusinessAppAccountService) getBusinessAppAccountDetail(id int64) (*pagination.PaginationResponse, error) {
	// 直接使用原有的GetBusinessAppAccountByID方法获取基础信息
	businessAppAccount, err := s.GetBusinessAppAccountByID(id)
	if err != nil {
		return nil, fmt.Errorf("获取业务应用账户详情失败: %v", err)
	}

	if businessAppAccount == nil {
		return &pagination.PaginationResponse{
			Data:       []BusinessAppAccount{},
			Total:      0,
			Page:       1,
			PageSize:   1,
			TotalPages: 0,
			HasNext:    false,
			HasPrev:    false,
		}, nil
	}

	// 构建响应
	response := &pagination.PaginationResponse{
		Data:       []BusinessAppAccount{*businessAppAccount},
		Total:      1,
		Page:       1,
		PageSize:   1,
		TotalPages: 1,
		HasNext:    false,
		HasPrev:    false,
	}

	return response, nil
}
