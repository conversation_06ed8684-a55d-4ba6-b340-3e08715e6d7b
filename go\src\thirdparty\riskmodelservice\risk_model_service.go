package riskmodelservice

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskthirdparty"

	"go.uber.org/zap"
)

// FailureType 失败类型枚举
const (
	FailureTypeInternalBlacklist = "internal_blacklist" // 内部黑名单
	FailureTypeExternalBlacklist = "external_blacklist" // 外部黑名单
	FailureTypeRiskPolicy        = "risk_policy"        // 风控准入策略
	FailureTypeRiskScore         = "risk_score"         // 风险评分
	FailureTypeThirdPartyData    = "third_party_data"   // 第三方数据
	FailureTypeModelCall         = "model_call"         // 模型调用失败
	FailureTypeModelPolicyCall   = "model_policy_call"  // 模型策略调用失败
	FailureTypeFeatureProcess    = "feature_process"    // 特征处理失败
	FailureTypeNetworkDuration   = "network_duration"   // 在网时长不足
)

// RiskModelConfig 风控模型配置
type RiskModelConfig struct {
	BaseURL string        `json:"base_url"`
	Timeout time.Duration `json:"timeout"`
}

// RCSRequest RCS接口请求参数
type RCSRequest struct {
	LeidaV4  interface{} `json:"leida_v4"`
	TanZhenC interface{} `json:"tan_zhen_c"`
}

// RCSResponse RCS接口响应
type RCSResponse struct {
	RiskResult int    `json:"riskResult"` // 0:通过，1:尽调，2:拒绝
	Message    string `json:"message"`
	Code       int    `json:"code"` // 200:成功，400:失败
	DataInput  string `json:"dataInput"`
}

// ModelScoreRequest 模型评分接口请求参数
type ModelScoreRequest struct {
	ProductID string                 `json:"productId"`
	Data      map[string]interface{} `json:"data"`
}

// ModelScoreResponse 模型评分接口响应
type ModelScoreResponse struct {
	Message    string  `json:"message"`
	Code       int     `json:"code"` // 200:成功，400:失败
	ModelScore float64 `json:"modelScore"`
}

// CreditLimitRequest 授信额度接口请求参数
type CreditLimitRequest struct {
	ModelScore float64 `json:"modelScore"`
	OtherScore float64 `json:"otherScore"`
}

// CreditLimitResponse 授信额度接口响应
type CreditLimitResponse struct {
	Message     string `json:"message"`
	Code        int    `json:"code"` // 200:成功，400:失败
	DataInput   string `json:"dataInput"`
	CreditLimit string `json:"creditLimit"`
}

// ProductData 产品数据结构
type ProductData struct {
	Code        string                 `json:"code"`
	Data        map[string]interface{} `json:"data"`
	Message     string                 `json:"message"`
	RequestID   string                 `json:"requestId"`
	RequestTime string                 `json:"requestTime"`
}

// RiskModelService 风控模型服务
type RiskModelService struct {
	config *RiskModelConfig
	client *http.Client
	logger *zap.Logger
}

// NewRiskModelService 创建新的风控模型服务
func NewRiskModelService() *RiskModelService {
	// 创建一个简单的logger，避免nil指针
	logger, _ := zap.NewDevelopment()
	if logger == nil {
		// 如果创建失败，使用nop logger
		logger = zap.NewNop()
	}

	return &RiskModelService{
		config: GetModelConfigFromGlobal(),
		client: createModelHTTPClient(),
		logger: logger,
	}
}

// GetModelConfigFromGlobal 从全局配置中获取风控模型服务配置
func GetModelConfigFromGlobal() *RiskModelConfig {
	if global.App == nil {
		panic("全局配置未初始化，请先初始化配置")
	}

	modelConfig := global.App.Config.RiskModel
	return &RiskModelConfig{
		BaseURL: modelConfig.BaseURL,
		Timeout: time.Duration(modelConfig.Timeout) * time.Second,
	}
}

// RiskResultResponse 风控准入策略响应
type RiskResultResponse struct {
	RiskResult  int      `json:"riskResult"` // 0:通过，1:尽调，2:拒绝
	Message     string   `json:"message"`
	Code        int      `json:"code"` // 200:成功，400:失败
	FailedRules []string `json:"failedRules"`
}

// RiskEvaluationResult 风控评估结果
type RiskEvaluationResult struct {
	ThirdPartyResults map[string]interface{} `json:"thirdPartyResults"`
	ModelResults      map[string]interface{} `json:"modelResults"`
	CreditLimit       *CreditLimitResponse   `json:"creditLimit"`
	FinalScore        float64                `json:"finalScore"`
	FinalResult       int                    `json:"finalResult"`
	FailureType       string                 `json:"failureType,omitempty"`
	FailureReason     string                 `json:"failureReason,omitempty"`
}

// ProcessRiskEvaluation 处理风控评估流程
// ProcessRiskEvaluationWithCustomer 使用客户信息进行风控评估
func (s *RiskModelService) ProcessRiskEvaluationWithCustomer(ctx context.Context, customerID int, riskRequest *riskthirdparty.RiskRequest) (*RiskEvaluationResult, error) {
	// todo mock fl 移除mock逻辑
	// if customerID != 26 {
	// 	return s.createMockApprovedResult(), nil
	// }
	return s.processRiskEvaluationInternal(ctx, riskRequest)
}

// createMockApprovedResult 创建模拟通过结果
func (s *RiskModelService) createMockApprovedResult() *RiskEvaluationResult {
	return &RiskEvaluationResult{
		FinalResult: model.APPROVED,
		FinalScore:  model.MaxRiskScore,
		CreditLimit: &CreditLimitResponse{
			CreditLimit: "10000",
			Code:        200,
			Message:     "success",
		},
		ThirdPartyResults: make(map[string]interface{}),
		ModelResults:      make(map[string]interface{}),
	}
}

// processRiskEvaluationInternal 内部风控评估处理逻辑
func (s *RiskModelService) processRiskEvaluationInternal(ctx context.Context, riskRequest *riskthirdparty.RiskRequest) (*RiskEvaluationResult, error) {
	result := &RiskEvaluationResult{
		ThirdPartyResults: make(map[string]interface{}),
		ModelResults:      make(map[string]interface{}),
	}

	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()

	// 1. 黑名单检查
	if blacklistResult := s.checkExternalBlacklist(thirdPartyService, riskRequest, result); blacklistResult != nil {
		return blacklistResult, nil
	}

	// 2. 在网时长检查
	if networkDurationResult := s.checkNetworkDuration(thirdPartyService, riskRequest, result); networkDurationResult != nil {
		return networkDurationResult, nil
	}

	// 3. 获取第三方数据
	tanZhenData, leidaData := s.fetchThirdPartyData(thirdPartyService, riskRequest, result)
	if tanZhenData == nil && leidaData == nil {
		//构造返回数据
		result.FinalResult = model.REJECTED
		result.FailureType = FailureTypeThirdPartyData
		result.FailureReason = "获取第三方数据失败"
		return result, nil
	}

	// 4. 处理特征数据并调用模型
	result, err := s.processAndEvaluateRisk(ctx, result, tanZhenData, leidaData)
	if err != nil {
		return nil, fmt.Errorf("处理特征数据并调用模型失败: %w", err)
	}

	return result, nil
}

// checkExternalBlacklist 检查外部黑名单
func (s *RiskModelService) checkExternalBlacklist(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) *RiskEvaluationResult {
	xdhmdThirdPartyData, err := s.getThirdPartyData(thirdPartyService, "xdhmd", riskRequest)
	if err != nil {
		global.App.Log.Error("获取黑名单数据失败", zap.Error(err))
		return nil // 黑名单数据获取失败，继续后续流程
	}

	// 检查黑名单状态
	if blackList, exists := xdhmdThirdPartyData["black_list"]; exists {
		if blackListStr, ok := blackList.(string); ok && blackListStr == "1" {
			// 命中黑名单，返回拒绝结果
			global.App.Log.Warn("用户命中外部黑名单，直接拒绝", zap.Any("blacklist_data", xdhmdThirdPartyData))
			return s.createBlacklistRejectedResult(result, xdhmdThirdPartyData)
		}
	}

	// 将黑名单数据保存到结果中
	result.ThirdPartyResults["blacklist_data"] = xdhmdThirdPartyData
	return nil
}

// checkNetworkDuration 检查在网时长
func (s *RiskModelService) checkNetworkDuration(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) *RiskEvaluationResult {
	zwscThirdPartyData, err := s.getThirdPartyData(thirdPartyService, "zwsc", riskRequest)
	if err != nil {
		global.App.Log.Error("获取在网时长数据失败", zap.Error(err))
		return nil // 在网时长数据获取失败，继续后续流程
	}

	// 检查查询结果是否成功
	if queryResult, exists := zwscThirdPartyData["queryResult"]; exists {
		if queryResultStr, ok := queryResult.(string); ok && queryResultStr == "1" {
			// 查得数据，检查在网时长
			if minValue, exists := zwscThirdPartyData["min"]; exists {
				// max为null表示两年以上，不需要拒绝
				if minValue != nil {
					if minInt, ok := minValue.(float64); ok {
						// 在网时长小于6个月的用户拒掉
						if minInt <= 6 {
							global.App.Log.Warn("用户在网时长不足6个月，直接拒绝",
								zap.Any("network_duration_data", zwscThirdPartyData),
								zap.Float64("max_duration", minInt))
							return s.createNetworkDurationRejectedResult(result, zwscThirdPartyData)
						}
					}
				}
			}
		}
	}

	// 将在网时长数据保存到结果中
	result.ThirdPartyResults["network_duration_data"] = zwscThirdPartyData
	return nil
}

// createBlacklistRejectedResult 创建黑名单拒绝结果
func (s *RiskModelService) createBlacklistRejectedResult(result *RiskEvaluationResult, blacklistData map[string]interface{}) *RiskEvaluationResult {
	result.FinalResult = model.REJECTED
	result.FinalScore = 0.0
	result.ThirdPartyResults["blacklist_data"] = blacklistData
	result.FailureType = FailureTypeExternalBlacklist

	if blacklistBytes, err := json.Marshal(blacklistData); err == nil {
		result.FailureReason = string(blacklistBytes)
	} else {
		result.FailureReason = "外部黑名单数据序列化失败"
	}

	return result
}

// createNetworkDurationRejectedResult 创建在网时长拒绝结果
func (s *RiskModelService) createNetworkDurationRejectedResult(result *RiskEvaluationResult, networkDurationData map[string]interface{}) *RiskEvaluationResult {
	result.FinalResult = model.REJECTED
	result.FinalScore = 0.0
	result.ThirdPartyResults["network_duration_data"] = networkDurationData
	result.FailureType = FailureTypeNetworkDuration

	if networkDurationBytes, err := json.Marshal(networkDurationData); err == nil {
		result.FailureReason = string(networkDurationBytes)
	} else {
		result.FailureReason = "在网时长数据序列化失败"
	}

	return result
}

// fetchThirdPartyData 获取第三方数据
// 获取tan_zhen_c数据
func (s *RiskModelService) fetchThirdPartyData(thirdPartyService *riskthirdparty.RiskThirdPartyService, riskRequest *riskthirdparty.RiskRequest, result *RiskEvaluationResult) (map[string]interface{}, map[string]interface{}) {
	tanZhenData, err := s.getThirdPartyData(thirdPartyService, "tan_zhen_c", riskRequest)
	if err != nil {
		global.App.Log.Error("获取tan_zhen_c第三方数据失败", zap.Error(err))
		tanZhenData = nil
	} else {
		result.ThirdPartyResults["tanZhen"] = tanZhenData
	}

	// 获取leida_v4数据
	leidaData, err := s.getThirdPartyData(thirdPartyService, "leida_v4", riskRequest)
	if err != nil {
		global.App.Log.Error("获取leida_v4第三方数据失败", zap.Error(err))
		leidaData = nil
	} else {
		result.ThirdPartyResults["leida"] = leidaData
	}

	return tanZhenData, leidaData
}

// processAndEvaluateRisk 处理特征数据并调用风控模型
func (s *RiskModelService) processAndEvaluateRisk(ctx context.Context, result *RiskEvaluationResult, tanZhenData, leidaData map[string]interface{}) (*RiskEvaluationResult, error) {
	// 1. 特征处理
	processedData, err := s.processFeatureData(ctx, leidaData, tanZhenData)
	if err != nil {
		global.App.Log.Error("特征处理失败", zap.Error(err))
		s.recordError(result, "feature_process_error", err.Error())
		result.FailureType = FailureTypeFeatureProcess
		result.FailureReason = err.Error()
		return result, fmt.Errorf("特征处理失败: %w", err)
	} else {
		result.ModelResults["processed_features"] = processedData
	}

	// 2. 调用风控模型接口
	riskResult, modelScore, err := s.callRiskModels(ctx, processedData, result)
	if err != nil {
		return result, fmt.Errorf("调用风控模型接口失败: %w", err)
	}

	// 3. 计算最终结果
	if err := s.calculateFinalResult(result, riskResult, modelScore); err != nil {
		return result, fmt.Errorf("计算最终结果失败: %w", err)
	}

	global.App.Log.Info("风控评估完成",
		zap.Float64("finalScore", result.FinalScore),
		zap.Int("finalResult", result.FinalResult),
		zap.Bool("hasCreditLimit", result.CreditLimit != nil))

	return result, nil
}

// recordError 记录错误到结果中
func (s *RiskModelService) recordError(result *RiskEvaluationResult, errorType, errorMsg string) {
	result.ThirdPartyResults[errorType] = errorMsg
	result.ModelResults[errorType] = errorMsg
}

// callRiskModels 调用风控模型接口
func (s *RiskModelService) callRiskModels(ctx context.Context, processedData map[string]interface{}, result *RiskEvaluationResult) (*RiskResultResponse, *ModelScoreResponse, error) {
	// 调用风控准入策略接口
	riskResult, err := s.callRiskResultAPI(ctx, processedData)
	if err != nil {
		global.App.Log.Error("风控准入策略调用失败", zap.Error(err))
		s.recordError(result, "risk_result_error", err.Error())
		result.FailureType = FailureTypeModelPolicyCall
		result.FailureReason = err.Error()
		return nil, nil, fmt.Errorf("风控准入策略调用失败: %w", err)
	} else {
		result.ModelResults["risk_result"] = riskResult
	}

	// 调用风控模型评分接口
	modelScore, err := s.callModelScoreAPI(ctx, processedData)
	if err != nil {
		global.App.Log.Error("风控模型评分调用失败", zap.Error(err))
		s.recordError(result, "model_score_error", err.Error())
		if result.FailureType == "" {
			result.FailureType = FailureTypeModelCall
			result.FailureReason = err.Error()
		}
		return riskResult, nil, fmt.Errorf("风控模型评分调用失败: %w", err)
	} else {
		result.ModelResults["model_score"] = modelScore
	}

	return riskResult, modelScore, nil
}

// calculateFinalResult 计算最终评分和结果
func (s *RiskModelService) calculateFinalResult(result *RiskEvaluationResult, riskResult *RiskResultResponse, modelScore *ModelScoreResponse) error {
	// 计算最终评分
	finalScore := 0.0
	if modelScore != nil && modelScore.Code == 200 {
		finalScore = modelScore.ModelScore
	} else {
		global.App.Log.Warn("使用默认评分", zap.Float64("score", finalScore))
		// 如果模型评分失败且之前没有设置失败类型，则设置为风控评分失败
		if result.FailureType == "" {
			result.FailureType = FailureTypeRiskScore
			result.FailureReason = "风控模型评分失败"
		}
	}
	result.FinalScore = finalScore

	// 确定最终结果
	if riskResult != nil && riskResult.Code == 200 {
		result.FinalResult = riskResult.RiskResult
		// 如果风控准入策略拒绝，记录失败原因
		if riskResult.RiskResult == model.REJECTED && len(riskResult.FailedRules) > 0 {
			result.FailureType = FailureTypeRiskPolicy
			if failedRulesBytes, err := json.Marshal(riskResult.FailedRules); err == nil {
				result.FailureReason = string(failedRulesBytes)
			} else {
				result.FailureReason = "风控准入策略失败规则序列化失败"
			}
		}
	} else {
		result.FinalResult = model.CALLRISKMODELFAILED
		// 如果风控准入策略调用失败且之前没有设置失败类型，则设置为模型调用失败
		if result.FailureType == "" {
			result.FailureType = FailureTypeModelCall
			result.FailureReason = "风控准入策略调用失败"
		}
	}

	return nil
}

// makeRequest 发送HTTP请求的通用方法
func (s *RiskModelService) makeRequest(ctx context.Context, method, url string, request interface{}, response interface{}) error {
	// 序列化请求参数
	jsonData, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, method, url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// 发送请求
	resp, err := s.client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	err = json.Unmarshal(body, response)
	if err != nil {
		return fmt.Errorf("解析响应失败: %v", err)
	}

	return nil
}

// createModelHTTPClient 创建HTTP客户端
func createModelHTTPClient() *http.Client {
	return &http.Client{
		Timeout: 30 * time.Second,
	}
}

// getThirdPartyData 获取第三方风控数据
func (s *RiskModelService) getThirdPartyData(thirdPartyService *riskthirdparty.RiskThirdPartyService, productID string, request *riskthirdparty.RiskRequest) (map[string]interface{}, error) {
	// 临时修改配置中的产品ID
	originalConfig := thirdPartyService.GetConfig()
	modifiedConfig := *originalConfig
	modifiedConfig.ProductID = productID
	thirdPartyService.SetConfig(&modifiedConfig)

	// 调用第三方接口
	response, err := thirdPartyService.QueryRiskData(request)
	if err != nil {
		global.App.Log.Error("调用第三方风控接口失败", zap.Error(err), zap.Any("response", response))
		return nil, fmt.Errorf("调用第三方风控接口失败: %v", err)
	}

	// 恢复原始配置
	thirdPartyService.SetConfig(originalConfig)

	return response.Data, nil
}

// processFeatureData 统一调用特征处理接口
func (s *RiskModelService) processFeatureData(ctx context.Context, leidaData, tanZhenData map[string]interface{}) (map[string]interface{}, error) {
	// 构建统一特征处理请求
	featureRequest := map[string]interface{}{
		"leida_v4":   leidaData,
		"tan_zhen_c": tanZhenData,
	}

	// 调用统一特征处理接口
	url := s.config.BaseURL + "/feature_process"
	var response map[string]interface{}
	err := s.makeRequest(ctx, "POST", url, featureRequest, &response)
	if err != nil {
		return nil, fmt.Errorf("调用特征处理接口失败: %v", err)
	}

	// 从响应的data字段中提取处理后的数据
	if data, ok := response["data"].(map[string]interface{}); ok {
		return data, nil
	}

	return response, nil
}

// callRiskResultAPI 调用风控准入策略接口
func (s *RiskModelService) callRiskResultAPI(ctx context.Context, processedData map[string]interface{}) (*RiskResultResponse, error) {
	//mock 返回数据 RiskResult 随机返回 0~2
	// riskResult := rand.Intn(3)
	// return &RiskResultResponse{
	// 	Code:       200,
	// 	Message:    "success",
	// 	RiskResult: riskResult,
	// }, nil

	// 调用风控准入策略接口
	url := s.config.BaseURL + "/product_1/riskResult"
	var response RiskResultResponse
	err := s.makeRequest(ctx, "POST", url, processedData, &response)
	if err != nil {
		return nil, fmt.Errorf("调用风控准入策略接口失败: %v", err)
	}

	return &response, nil
}

// callModelScoreAPI 调用风控模型评分接口
func (s *RiskModelService) callModelScoreAPI(ctx context.Context, processedData map[string]interface{}) (*ModelScoreResponse, error) {
	//mock 返回数据 ModelScore 随机返回 0~100 浮点树
	// modelScore := rand.Float64() * 100
	// return &ModelScoreResponse{
	// 	Code:       200,
	// 	Message:    "success",
	// 	ModelScore: modelScore,
	// }, nil

	// 调用风控模型评分接口
	url := s.config.BaseURL + "/product_1/modelScore"
	var response ModelScoreResponse
	err := s.makeRequest(ctx, "POST", url, processedData, &response)
	if err != nil {
		return nil, fmt.Errorf("调用风控模型评分接口失败: %v", err)
	}

	return &response, nil
}

// callCreditLimitAPI 调用授信额度接口
func (s *RiskModelService) callCreditLimitAPI(ctx context.Context, processedData map[string]interface{}) (float64, error) {
	//mock 返回数据 CreditLimit 随机返回 0~1000000
	// creditLimit := rand.Float64() * 1000000
	// return creditLimit, nil

	url := s.config.BaseURL + "/product_1/creditLimit"
	var response map[string]interface{}
	err := s.makeRequest(ctx, "POST", url, processedData, &response)
	if err != nil {
		return 0, fmt.Errorf("调用授信额度接口失败: %v", err)
	}

	// 从响应中提取授信额度
	if limit, ok := response["credit_limit"].(float64); ok {
		return limit, nil
	}
	return 0, fmt.Errorf("无法解析授信额度响应")
}

// combineThirdPartyData 合并第三方数据
func (s *RiskModelService) combineThirdPartyData(tanZhenData, leidaData map[string]interface{}) map[string]interface{} {
	combinedData := make(map[string]interface{})

	// 添加tan_zhen_c数据
	if tanZhenData != nil {
		combinedData["tan_zhen_c"] = tanZhenData
	}

	// 添加leida_v4数据
	if leidaData != nil {
		combinedData["leida_v4"] = leidaData
	}

	// 添加合并标识
	combinedData["data_source"] = "combined_thirdparty"
	combinedData["merge_time"] = time.Now().Format("2006-01-02 15:04:05")

	return combinedData
}
