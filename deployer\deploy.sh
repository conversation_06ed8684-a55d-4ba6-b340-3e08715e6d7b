#!/bin/bash

# Fincore Local Deployment Script
# 在目标服务器上本地执行的部署脚本
# Author: System Administrator
# Version: 1.0.0

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEPLOYER_DIR="$SCRIPT_DIR"

# 部署配置
DEPLOY_PATH="${DEPLOY_PATH:-/opt/fincore}"
INSTALL_USER="${INSTALL_USER:-fincore}"
INSTALL_GROUP="${INSTALL_GROUP:-fincore}"

# 日志函数
log_info() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
Fincore本地部署脚本

用法: $0 [选项]

选项:
  -d, --dest PATH       目标部署路径 (默认: /opt/fincore)
  -u, --user USER       安装用户 (默认: fincore)
  -g, --group GROUP     安装用户组 (默认: fincore)
  -s, --start           部署后启动服务
  -r, --restart         部署后重启服务
  -c, --check           部署后检查服务状态
  -n, --nginx           部署Nginx配置
  -h, --help            显示帮助信息

环境变量:
  DEPLOY_PATH           部署路径
  INSTALL_USER          安装用户
  INSTALL_GROUP         安装用户组

使用流程:
  1. 在开发机器上执行构建: ./build.sh
  2. 将整个deployer目录上传到目标服务器
  3. 在目标服务器上执行: sudo ./deploy.sh

示例:
  sudo ./deploy.sh
  sudo ./deploy.sh --start
  sudo ./deploy.sh -d /opt/fincore --start
  sudo ./deploy.sh --start --nginx

EOF
}

# 解析命令行参数
parse_arguments() {
    local start_after_deploy=false
    local restart_after_deploy=false
    local check_after_deploy=false
    local deploy_nginx=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--dest)
                DEPLOY_PATH="$2"
                shift 2
                ;;
            -u|--user)
                INSTALL_USER="$2"
                shift 2
                ;;
            -g|--group)
                INSTALL_GROUP="$2"
                shift 2
                ;;
            -s|--start)
                start_after_deploy=true
                shift
                ;;
            -r|--restart)
                restart_after_deploy=true
                shift
                ;;
            -c|--check)
                check_after_deploy=true
                shift
                ;;
            -n|--nginx)
                deploy_nginx=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                log_error "多余的参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 导出变量供其他函数使用
    export START_AFTER_DEPLOY="$start_after_deploy"
    export RESTART_AFTER_DEPLOY="$restart_after_deploy"
    export CHECK_AFTER_DEPLOY="$check_after_deploy"
    export DEPLOY_NGINX="$deploy_nginx"
}

# 验证部署配置
validate_config() {
    log_header "验证部署配置"
    
    log_info "当前工作目录: $(pwd)"
    log_info "脚本目录: $SCRIPT_DIR"
    log_info "deployer目录: $DEPLOYER_DIR"
    log_info "部署路径: $DEPLOY_PATH"
    log_info "安装用户: $INSTALL_USER"
    log_info "安装用户组: $INSTALL_GROUP"
    
    # 检查是否以root权限运行
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
    
    # 检查deployer目录
    if [[ ! -d "$DEPLOYER_DIR" ]]; then
        log_error "deployer目录不存在: $DEPLOYER_DIR"
        log_info "请确保在deployer目录中执行此脚本"
        exit 1
    fi
    
    # 检查构建产物
    if [[ ! -d "$DEPLOYER_DIR/build" ]]; then
        log_error "构建产物不存在: $DEPLOYER_DIR/build"
        log_info "请先运行构建脚本: ./build.sh"
        log_info "可用的目录: $(ls -la "$DEPLOYER_DIR/" | grep '^d' | awk '{print $9}' | tr '\n' ' ')"
        exit 1
    fi
    
    # 验证通过，无需额外检查
    
    log_success "配置验证通过"
}

# 检查系统要求
check_system_requirements() {
    log_header "检查系统要求"
    
    # 检查操作系统
    if ! command -v systemctl >/dev/null 2>&1; then
        log_error "系统不支持systemd，无法安装"
        exit 1
    fi
    
    # 检查必要命令
    local required_commands=("curl" "grep" "awk" "sed" "ps" "netstat")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            log_warn "缺少必要命令: $cmd"
            log_info "尝试安装..."
            
            if command -v apt-get >/dev/null 2>&1; then
                apt-get update && apt-get install -y "$cmd" || true
            elif command -v yum >/dev/null 2>&1; then
                yum install -y "$cmd" || true
            elif command -v dnf >/dev/null 2>&1; then
                dnf install -y "$cmd" || true
            fi
        fi
    done
    
    log_success "系统要求检查完成"
}

# 创建用户和组
create_user_group() {
    log_header "创建用户和组"
    
    # 创建组
    if ! getent group "$INSTALL_GROUP" >/dev/null 2>&1; then
        groupadd "$INSTALL_GROUP"
        log_success "创建组: $INSTALL_GROUP"
    else
        log_info "组已存在: $INSTALL_GROUP"
    fi
    
    # 创建用户
    if ! getent passwd "$INSTALL_USER" >/dev/null 2>&1; then
        useradd -r -g "$INSTALL_GROUP" -d "$DEPLOY_PATH" -s /bin/bash "$INSTALL_USER"
        log_success "创建用户: $INSTALL_USER"
    else
        log_info "用户已存在: $INSTALL_USER"
    fi
}

# 准备部署环境
prepare_deploy_environment() {
    log_header "准备部署环境"
    
    # 创建部署目录
    mkdir -p "$DEPLOY_PATH"
    mkdir -p "$DEPLOY_PATH/backup"
    mkdir -p "$DEPLOY_PATH/logs"
    
    # 备份当前版本（如果存在）
    if [[ -d "$DEPLOY_PATH/build" ]]; then
        local backup_dir="$DEPLOY_PATH/backup/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        cp -r "$DEPLOY_PATH/build" "$backup_dir/" 2>/dev/null || true
        log_success "已备份当前版本到: $backup_dir"
    fi
    
    log_success "部署环境准备完成"
}

# 复制文件到部署目录
copy_files() {
    log_header "复制文件到部署目录"

    # 在复制前停止服务，避免"Text file busy"错误
    log_info "检查并停止运行中的服务..."
    if systemctl is-active fincore >/dev/null 2>&1; then
        log_info "停止fincore服务..."
        systemctl stop fincore
        log_success "fincore服务已停止"
    fi



    # 确保所有fincore进程都已停止
    if pgrep -f "fincore" >/dev/null; then
        log_info "终止残留的fincore进程..."
        pkill -f "fincore" || true
        sleep 2

        # 如果还有进程，强制终止
        if pgrep -f "fincore" >/dev/null; then
            log_info "强制终止残留进程..."
            pkill -9 -f "fincore" || true
            sleep 1
        fi
        log_success "所有fincore进程已停止"
    fi

    log_info "复制构建产物..."

    # 复制build目录
    if [[ -d "$DEPLOYER_DIR/build" ]]; then
        cp -r "$DEPLOYER_DIR/build" "$DEPLOY_PATH/"
        log_success "复制构建产物完成"
    else
        log_error "构建产物不存在: $DEPLOYER_DIR/build"
        exit 1
    fi

    # 创建systemd服务文件目录
    mkdir -p "$DEPLOY_PATH/systemd"
    log_success "创建systemd目录完成"

    # 复制其他文件
    if [[ -f "$DEPLOYER_DIR/README.md" ]]; then
        cp "$DEPLOYER_DIR/README.md" "$DEPLOY_PATH/"
    fi

    # 确保配置文件在正确位置
    log_info "检查和设置配置文件..."
    local correct_config_dir="$DEPLOY_PATH/build/app/bin/resource"
    local correct_config_file="$correct_config_dir/config.yml"

    # 创建配置目录
    mkdir -p "$correct_config_dir"

    # 检查是否有示例配置文件
    if [[ ! -f "$correct_config_file" ]]; then
        if [[ -f "$DEPLOYER_DIR/config_example_aliyun.yml" ]]; then
            cp "$DEPLOYER_DIR/config_example_aliyun.yml" "$correct_config_file"
            log_success "复制阿里云示例配置文件到: $correct_config_file"
        elif [[ -f "$DEPLOYER_DIR/config_example_basic.yml" ]]; then
            cp "$DEPLOYER_DIR/config_example_basic.yml" "$correct_config_file"
            log_success "复制基本示例配置文件到: $correct_config_file"
        else
            log_warn "配置文件不存在，需要手动创建: $correct_config_file"
        fi
    else
        log_info "配置文件已存在: $correct_config_file"
    fi

    # 设置文件权限
    log_info "设置文件权限..."
    chown -R "$INSTALL_USER:$INSTALL_GROUP" "$DEPLOY_PATH"

    # 设置可执行权限 - 应用程序
    if [[ -f "$DEPLOY_PATH/build/app/bin/fincore" ]]; then
        chmod +x "$DEPLOY_PATH/build/app/bin/fincore"
        log_info "设置fincore二进制文件权限"
    fi

    if [[ -d "$DEPLOY_PATH/build/app/bin" ]]; then
        chmod +x "$DEPLOY_PATH/build/app/bin"/*.sh 2>/dev/null || true
    fi

    # 创建守护进程启动脚本
    log_info "创建守护进程启动脚本..."
    cat > "$DEPLOY_PATH/build/app/bin/fincore-daemon.sh" << 'EOF'
#!/bin/bash

# Fincore守护进程启动脚本
set -e

WORK_DIR="/opt/fincore/build/app/bin"
PID_FILE="$WORK_DIR/logs/fincore.pid"
LOG_FILE="$WORK_DIR/logs/fincore.out"

cd "$WORK_DIR"
mkdir -p logs

# 检查是否已经在运行
if [[ -f "$PID_FILE" ]]; then
    if kill -0 $(cat "$PID_FILE") 2>/dev/null; then
        echo "Fincore is already running (PID: $(cat "$PID_FILE"))"
        exit 0
    else
        echo "Removing stale PID file"
        rm -f "$PID_FILE"
    fi
fi

# 启动fincore进程
echo "Starting Fincore..."
nohup ./fincore > "$LOG_FILE" 2>&1 &
PID=$!

# 写入PID文件
echo $PID > "$PID_FILE"
echo "Fincore started with PID: $PID"

# 等待进程稳定
sleep 3

# 验证进程是否还在运行
if kill -0 $PID 2>/dev/null; then
    echo "Fincore started successfully"
    exit 0
else
    echo "Failed to start Fincore"
    rm -f "$PID_FILE"
    exit 1
fi
EOF

    chmod +x "$DEPLOY_PATH/build/app/bin/fincore-daemon.sh"
    chown fincore:fincore "$DEPLOY_PATH/build/app/bin/fincore-daemon.sh"
    log_success "创建守护进程启动脚本完成"

    # 创建停止脚本
    log_info "创建停止脚本..."
    cat > "$DEPLOY_PATH/build/app/bin/fincore-stop.sh" << 'EOF'
#!/bin/bash

# Fincore停止脚本
WORK_DIR="/opt/fincore/build/app/bin"
PID_FILE="$WORK_DIR/logs/fincore.pid"

cd "$WORK_DIR"

if [[ ! -f "$PID_FILE" ]]; then
    echo "PID file not found, Fincore may not be running"
    exit 0
fi

PID=$(cat "$PID_FILE")
if ! kill -0 $PID 2>/dev/null; then
    echo "Process $PID not found, removing stale PID file"
    rm -f "$PID_FILE"
    exit 0
fi

echo "Stopping Fincore (PID: $PID)..."

# 尝试优雅停止
kill -TERM $PID

# 等待进程停止
for i in {1..30}; do
    if ! kill -0 $PID 2>/dev/null; then
        echo "Fincore stopped successfully"
        rm -f "$PID_FILE"
        exit 0
    fi
    sleep 1
done

# 强制停止
echo "Force stopping Fincore..."
kill -KILL $PID 2>/dev/null || true
rm -f "$PID_FILE"
echo "Fincore force stopped"
EOF

    chmod +x "$DEPLOY_PATH/build/app/bin/fincore-stop.sh"
    chown fincore:fincore "$DEPLOY_PATH/build/app/bin/fincore-stop.sh"
    log_success "创建停止脚本完成"

    # 设置应用目录权限
    log_info "设置应用目录权限..."
    chown -R fincore:fincore "$DEPLOY_PATH/build"
    log_success "应用目录权限设置完成"

    log_success "文件复制完成"
}

# 安装fincore服务
install_fincore_service() {
    log_header "安装Fincore服务"

    log_info "创建systemd服务文件..."

    # 创建fincore.service文件
    cat > /etc/systemd/system/fincore.service << EOF
[Unit]
Description=Fincore Application Service
Documentation=https://github.com/fincore/fincore
After=network.target network-online.target
Wants=network-online.target

[Service]
Type=forking
User=fincore
Group=fincore
WorkingDirectory=$DEPLOY_PATH/build/app/bin
ExecStart=$DEPLOY_PATH/build/app/bin/fincore-daemon.sh
PIDFile=$DEPLOY_PATH/build/app/bin/logs/fincore.pid
ExecReload=/bin/kill -HUP \$MAINPID
ExecStop=$DEPLOY_PATH/build/app/bin/fincore-stop.sh

# 重启策略
Restart=on-failure
RestartSec=10
StartLimitInterval=300
StartLimitBurst=3

# 资源限制
LimitNOFILE=65536

# 环境变量
Environment=PATH=/usr/local/bin:/usr/bin:/bin
Environment=FINCORE_ENV=production

# 日志设置
StandardOutput=journal
StandardError=journal
SyslogIdentifier=fincore

# 进程管理
KillMode=mixed
KillSignal=SIGTERM
TimeoutStartSec=120
TimeoutStopSec=30

# PID文件管理
RemainAfterExit=no

[Install]
WantedBy=multi-user.target
EOF

    log_success "创建fincore.service完成"

    # 重新加载systemd
    systemctl daemon-reload
    log_success "重新加载systemd配置"

    # 启用服务
    systemctl enable fincore
    log_success "启用fincore服务"
}

# 部署Nginx配置
deploy_nginx_config() {
    if [[ "$DEPLOY_NGINX" == "true" ]]; then
        log_header "部署Nginx配置"

        # 检查nginx目录是否存在
        if [[ -d "$DEPLOY_PATH/nginx" ]]; then
            log_info "Nginx配置目录已存在，跳过复制"
        else
            # 复制nginx配置目录
            if [[ -d "$DEPLOYER_DIR/nginx" ]]; then
                cp -r "$DEPLOYER_DIR/nginx" "$DEPLOY_PATH/"
                chown -R root:root "$DEPLOY_PATH/nginx"
                log_success "复制Nginx配置目录完成"
            else
                log_error "Nginx配置目录不存在: $DEPLOYER_DIR/nginx"
                return 1
            fi
        fi

        # 执行nginx部署脚本
        if [[ -x "$DEPLOY_PATH/nginx/deploy-nginx.sh" ]]; then
            log_info "执行Nginx部署脚本..."
            cd "$DEPLOY_PATH/nginx"
            if ./deploy-nginx.sh; then
                log_success "Nginx配置部署完成"
            else
                log_error "Nginx配置部署失败"
                return 1
            fi
        else
            log_error "Nginx部署脚本不存在或不可执行"
            return 1
        fi
    fi
}

# 启动服务
start_services() {
    if [[ "$START_AFTER_DEPLOY" == "true" ]]; then
        log_header "启动服务"

        if systemctl start fincore; then
            log_success "fincore服务启动完成"
        else
            log_error "fincore服务启动失败"
        fi
    fi
}

# 重启服务
restart_services() {
    if [[ "$RESTART_AFTER_DEPLOY" == "true" ]]; then
        log_header "重启服务"

        # 停止服务
        systemctl stop fincore 2>/dev/null || true

        # 等待服务完全停止
        sleep 3

        # 启动服务
        if systemctl start fincore; then
            log_success "fincore服务重启完成"
        else
            log_error "fincore服务重启失败"
        fi
    fi
}

# 检查服务状态
check_services() {
    if [[ "$CHECK_AFTER_DEPLOY" == "true" ]]; then
        log_header "检查服务状态"

        # 检查fincore服务状态
        if systemctl is-active fincore >/dev/null 2>&1; then
            log_success "fincore服务运行正常"
        else
            log_error "fincore服务未运行"
        fi

        # 检查端口监听
        if netstat -tuln 2>/dev/null | grep -q ":8108" || ss -tuln 2>/dev/null | grep -q ":8108"; then
            log_success "端口8108正在监听"
        else
            log_error "端口8108未监听"
        fi

        log_success "服务状态检查完成"
    fi
}

# 显示部署摘要
show_deploy_summary() {
    log_header "部署摘要"

    echo "部署完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "部署路径: $DEPLOY_PATH"
    echo "安装用户: $INSTALL_USER:$INSTALL_GROUP"
    echo ""
    echo "后续操作:"
    echo "1. 检查服务状态:"
    echo "   sudo systemctl status fincore"
    echo ""
    echo "2. 查看日志:"
    echo "   sudo journalctl -u fincore -f"
    echo "   tail -f $DEPLOY_PATH/build/app/bin/logs/fincore.out"
    echo ""
    echo "3. 管理服务:"
    echo "   sudo systemctl start fincore"
    echo "   sudo systemctl stop fincore"
    echo "   sudo systemctl restart fincore"
    echo ""
    echo "4. 检查端口监听:"
    echo "   netstat -tuln | grep 8108"
    echo ""
    echo "5. 测试连接:"
    echo "   telnet localhost 8108"
    echo ""
    echo "故障排除:"
    echo "   如果服务启动失败，可以查看日志："
    echo "   sudo journalctl -u fincore -n 50"
    echo "   sudo systemctl status fincore"
    echo ""
}

# 主部署流程
main() {
    local start_time
    start_time=$(date +%s)

    log_header "Fincore项目本地部署开始"

    parse_arguments "$@"
    validate_config
    check_system_requirements
    create_user_group
    prepare_deploy_environment
    copy_files
    install_fincore_service
    deploy_nginx_config
    start_services
    restart_services
    check_services

    local end_time
    end_time=$(date +%s)
    local duration=$((end_time - start_time))

    log_success "部署完成！耗时: ${duration}秒"
    show_deploy_summary
}

# 脚本入口
main "$@"
