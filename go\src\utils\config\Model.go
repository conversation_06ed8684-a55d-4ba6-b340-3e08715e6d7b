package config

// 数据数据库配置
type DBconf struct {
	Driver   string `yaml:"driver"`
	Hostname string `yaml:"hostname"`
	Hostport string `yaml:"hostport"`
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Database string `yaml:"database"`
	Prefix   string `yaml:"prefix"`
}

// 应用配置
type App struct {
	Port                string `yaml:"port"`
	Hostname            string `yaml:"hostname"`
	H5port              string `yaml:"h5port"`
	H5Protocol          string `yaml:"h5protocol"` // H5服务的协议，http或https
	Version             string `yaml:"version"`
	Env                 string `yaml:"env"`
	Apisecret           string `yaml:"apisecret"`
	Allowurl            string `yaml:"allowurl"`
	TokenOutTime        string `yaml:"tokenouttime"`
	CPUnum              string `yaml:"cpunum"`
	Domain              string `yaml:"domain"`
	Vueobjroot          string `yaml:"vueobjroot"`
	CompanyPrivateHouse string `yaml:"companyPrivateHouse"`
	Rootview            string `yaml:"rootview"`
	RunlogType          string `yaml:"runlogtype"`
	NoVerifyTokenRoot   string `yaml:"noVerifyTokenRoot"`
	NoVerifyAPIRoot     string `yaml:"noVerifyAPIRoot"`
	NoVerifyToken       string `yaml:"noVerifyToken"`
	NoVerifyAPI         string `yaml:"noVerifyAPI"`
	InvitationPage      string `yaml:"invitationPage"` // 邀请链接页面路径，默认为 "/login"
	FileServer          string `yaml:"fileServer"`     // 文件服务地址，后续要改成 oss
}

// JWT验证
type Jwt struct {
	Secret string `mapstructure:"secret" json:"secret" yaml:"secret"`
	JwtTtl int64  `mapstructure:"jwt_ttl" json:"jwt_ttl" yaml:"jwt_ttl"` // token 有效期（秒）
}

// 日志文件
type Log struct {
	Level      string `mapstructure:"level" json:"level" yaml:"level"`
	RootDir    string `mapstructure:"root_dir" json:"root_dir" yaml:"root_dir"`
	Format     string `mapstructure:"format" json:"format" yaml:"format"`
	Output     string `mapstructure:"output" json:"output" yaml:"output"`       // 输出方式：file/console/both
	Timezone   string `mapstructure:"timezone" json:"timezone" yaml:"timezone"` // 时区：Asia/Shanghai, UTC, America/New_York 等
	ShowLine   bool   `mapstructure:"show_line" json:"show_line" yaml:"show_line"`
	MaxBackups int    `mapstructure:"max_backups" json:"max_backups" yaml:"max_backups"`
	MaxSize    int    `mapstructure:"max_size" json:"max_size" yaml:"max_size"` // MB
	MaxAge     int    `mapstructure:"max_age" json:"max_age" yaml:"max_age"`    // day
	Compress   bool   `mapstructure:"compress" json:"compress" yaml:"compress"`
}

// 短信服务配置
type Sms struct {
	CodeAccount      string `mapstructure:"code_account" json:"code_account" yaml:"code_account"`
	CodePassword     string `mapstructure:"code_password" json:"code_password" yaml:"code_password"`
	BusinessAccount  string `mapstructure:"business_account" json:"business_account" yaml:"business_account"`
	BusinessPassword string `mapstructure:"business_password" json:"business_password" yaml:"business_password"`
	Sign             string `mapstructure:"sign" json:"sign" yaml:"sign"`
	Apiurl           string `mapstructure:"apiurl" json:"apiurl" yaml:"apiurl"`
	Isused           string `mapstructure:"isused" json:"isused" yaml:"isused"`
}

type Vbank struct {
	Apikey string `mapstructure:"apikey" json:"apikey" yaml:"apikey"`
	Apiurl string `mapstructure:"apiurl" json:"apiurl" yaml:"apiurl"`
}

type Aiqian_envs struct {
	App_id     string `mapstructure:"app_id" json:"level" yaml:"level"`
	Pro_host   string `mapstructure:"pro_host" json:"level" yaml:"level"`
	PrivateKey string `mapstructure:"privateKey" json:"level" yaml:"level"`
}

// 商盟 统统付
type SumPay struct {
	Environment    string `mapstructure:"environment" json:"environment" yaml:"environment"`                // test, prod
	TestUrl        string `mapstructure:"test_url" json:"test_url" yaml:"test_url"`                         // 测试环境地址
	ProdUrl        string `mapstructure:"prod_url" json:"prod_url" yaml:"prod_url"`                         // 生产环境地址
	MerNo          string `mapstructure:"mer_no" json:"mer_no" yaml:"mer_no"`                               // 商户号
	MerNoGuarantee string `mapstructure:"mer_no_guarantee" json:"mer_no_guarantee" yaml:"mer_no_guarantee"` // 担保账户
	AppId          string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`                               // 应用ID
	Domain         string `mapstructure:"domain" json:"domain" yaml:"domain"`                               // 域名
	NotifyUrl      string `mapstructure:"notify_url" json:"notify_url" yaml:"notify_url"`                   // 通知地址
	SkipTLSVerify  bool   `mapstructure:"skip_tls_verify" json:"skip_tls_verify" yaml:"skip_tls_verify"`    // 是否跳过TLS验证
	RequestTimeout int    `mapstructure:"request_timeout" json:"request_timeout" yaml:"request_timeout"`    // 请求超时时间
	AssetMerNo     string `mapstructure:"asset_mer_no" json:"asset_mer_no" yaml:"asset_mer_no"`             // 资产业务商户号
	GuaranteeMerNo string `mapstructure:"guarantee_mer_no" json:"guarantee_mer_no" yaml:"guarantee_mer_no"` // 担保业务商户号
	RetryCount     int    `mapstructure:"retry_count" json:"retry_count" yaml:"retry_count"`                // 重试次数
	RetryWaitTime  int    `mapstructure:"retry_wait_time" json:"retry_wait_time" yaml:"retry_wait_time"`    // 重试等待时间
	Cert           Cert   `mapstructure:"cert" json:"cert" yaml:"cert"`                                     // 证书
}

// RiskEvaluation 风控评估配置
type RiskEvaluation struct {
	EvaluationExpiryHours int `mapstructure:"evaluation_expiry_hours" json:"evaluation_expiry_hours" yaml:"evaluation_expiry_hours"` // 评估有效期（小时）
}

// Redis 配置
type Redis struct {
	Host     string `mapstructure:"host" json:"host" yaml:"host"`             // 连接地址
	Port     string `mapstructure:"port" json:"port" yaml:"port"`             // 端口
	Password string `mapstructure:"password" json:"password" yaml:"password"` // 密码
	DB       int    `mapstructure:"db" json:"db" yaml:"db"`                   // 数据库编号
	Timeout  int    `mapstructure:"timeout" json:"timeout" yaml:"timeout"`    // 链接超时 单位秒
}

type Cert struct {
	PrivateKeyPath string `mapstructure:"private_key_path" json:"private_key_path" yaml:"private_key_path"` // 私钥路径
	PublicKeyPath  string `mapstructure:"public_key_path" json:"public_key_path" yaml:"public_key_path"`    // 公钥路径
	Password       string `mapstructure:"password" json:"password" yaml:"password"`                         // 证书密码
}

// 第三方风控配置
type RiskThirdParty struct {
	MerchantID string `mapstructure:"merchant_id" json:"merchant_id" yaml:"merchant_id"` // 商户ID
	AESKey     string `mapstructure:"aes_key" json:"aes_key" yaml:"aes_key"`             // AES加密密钥
	URL        string `mapstructure:"url" json:"url" yaml:"url"`                         // 第三方风控接口地址
	ProductID  string `mapstructure:"product_id" json:"product_id" yaml:"product_id"`    // 产品ID
	Timeout    int    `mapstructure:"timeout" json:"timeout" yaml:"timeout"`             // 请求超时时间（秒）
}

// 风控模型服务配置
type RiskModel struct {
	BaseURL string `mapstructure:"base_url" json:"base_url" yaml:"base_url"` // 风控模型API地址
	Timeout int    `mapstructure:"timeout" json:"timeout" yaml:"timeout"`    // 请求超时时间（秒）
}

// 第三方风控服务配置 (Third Party Risk Service)
type ThirdRisk struct {
	Enabled     bool   `mapstructure:"enabled" json:"enabled" yaml:"enabled"`                // 是否启用第三方风控服务
	URL         string `mapstructure:"url" json:"url" yaml:"url"`                            // 第三方风控接口地址
	AppID       string `mapstructure:"app_id" json:"app_id" yaml:"app_id"`                   // 应用ID
	AESKey      string `mapstructure:"aes_key" json:"aes_key" yaml:"aes_key"`                // AES加密密钥
	Timeout     int    `mapstructure:"timeout" json:"timeout" yaml:"timeout"`                // 请求超时时间（秒）
	ServiceCode string `mapstructure:"service_code" json:"service_code" yaml:"service_code"` // 服务码
	DataSource  string `mapstructure:"data_source" json:"data_source" yaml:"data_source"`    // 数据源标识
}

// 系统代扣配置
type SystemDeduct struct {
	MaxFailCount int `mapstructure:"max_fail_count" json:"max_fail_count" yaml:"max_fail_count"` // 银行卡系统代扣最大失败次数
}
