import { mergeConfig } from 'vite';
import baseConfig from './vite.config.base';

export default mergeConfig(
  {
    mode: 'development',
    server: {
      open: false,
      fs: {
        strict: true,
      },
      host: true,
      port:9106,
      proxy: {
        '/business': {
          target: 'http://************:8108',//代理的地址
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/business/, '/business')//这里的/需要转义
        },
        '/resource': {
          target: 'http://sg.goflys.cn/resource',//代理的地址
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/resource/, '')//这里的/需要转义
        }
      }
    },
    plugins: [
    ],
  },
  baseConfig
);
