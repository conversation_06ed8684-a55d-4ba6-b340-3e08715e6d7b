package model

import (
	"fmt"
	"strconv"
	"time"
)

// OCRIdentityRecord OCR身份证识别记录
type OCRIdentityRecord struct {
	ID         uint32    `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	UserID     uint32    `json:"user_id" gorm:"not null;index"`
	OrderID    string    `json:"order_id" gorm:"size:100"`
	Sex        string    `json:"sex" gorm:"size:10"`
	Nation     string    `json:"nation" gorm:"size:50"`
	Born       string    `json:"born" gorm:"size:20"`
	Address    string    `json:"address" gorm:"type:text"`
	IDCard     string    `json:"id_card" gorm:"size:50;index"`
	BeginDate  time.Time `json:"begin_date" gorm:"type:date"`
	EndDate    time.Time `json:"end_date" gorm:"type:date"`
	Department string    `json:"department" gorm:"size:200"`
	CreatedAt  time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (OCRIdentityRecord) TableName() string {
	return "ocr_identity_records"
}

// OCRIdentityRecordService OCR身份证识别记录服务
type OCRIdentityRecordService struct{}

// NewOCRIdentityRecordService 创建OCR身份证识别记录服务实例
func NewOCRIdentityRecordService() *OCRIdentityRecordService {
	return &OCRIdentityRecordService{}
}

// SaveOrUpdateOCRRecord 保存或更新OCR识别记录（有则更新，无则新增）
func (s *OCRIdentityRecordService) SaveOrUpdateOCRRecord(record *OCRIdentityRecord) error {
	if record == nil {
		return fmt.Errorf("OCR记录不能为空")
	}
	if record.UserID <= 0 {
		return fmt.Errorf("用户ID不能为空")
	}

	// 检查是否已存在记录（根据用户ID和身份证号）
	existingRecord, err := DB().Table("ocr_identity_records").
		Where("user_id", record.UserID).
		First()

	if err != nil {
		// 查询出错，返回错误
		return fmt.Errorf("查询OCR记录失败: %v", err)
	}
	data := map[string]interface{}{
		"user_id": record.UserID,
	}

	if record.IDCard != "" {
		// 构建身份证相关数据
		data["order_id"] = record.OrderID
		data["sex"] = record.Sex
		data["nation"] = record.Nation
		data["born"] = record.Born
		data["address"] = record.Address
		data["id_card"] = record.IDCard
	} else {
		data["department"] = record.Department
		// 构建日期相关数据
		// 将 time.Time 转换为数据库可接受的日期格式
		if !record.BeginDate.IsZero() {
			data["begin_date"] = record.BeginDate.Format("2006-01-02")
		} else {
			data["begin_date"] = nil
		}
		if !record.EndDate.IsZero() {
			data["end_date"] = record.EndDate.Format("2006-01-02")
		} else {
			data["end_date"] = nil
		}
	}

	if len(existingRecord) > 0 {
		// 记录存在，执行更新
		_, err = DB().Table("ocr_identity_records").
			Where("user_id", record.UserID).
			Data(data).Update()
		if err != nil {
			return fmt.Errorf("更新OCR记录失败: %v", err)
		}
	} else {
		// 记录不存在，执行插入
		_, err = DB().Table("ocr_identity_records").Data(data).Insert()
		if err != nil {
			return fmt.Errorf("保存OCR记录失败: %v", err)
		}
	}

	return nil
}

// mapToOCRRecord 专门用于将数据映射到OCRIdentityRecord结构体，处理类型转换
func mapToOCRRecord(data map[string]interface{}, record *OCRIdentityRecord) error {
	// 辅助函数：安全地转换为uint32
	parseUint32 := func(v interface{}) uint32 {
		switch val := v.(type) {
		case uint32:
			return val
		case int:
			return uint32(val)
		case int64:
			return uint32(val)
		case float64:
			return uint32(val)
		case string:
			if i, err := strconv.ParseUint(val, 10, 32); err == nil {
				return uint32(i)
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为time.Time
	parseTime := func(v interface{}) time.Time {
		switch val := v.(type) {
		case time.Time:
			return val
		case string:
			// 尝试解析常见的时间格式，优先处理ISO 8601格式
			formats := []string{
				// ISO 8601格式（数据库常用）
				"2006-01-02T15:04:05-07:00",     // 带时区偏移
				"2006-01-02T15:04:05+08:00",     // 中国时区
				"2006-01-02T15:04:05Z",          // UTC时区
				"2006-01-02T15:04:05.000Z",      // 带毫秒的UTC
				"2006-01-02T15:04:05.000-07:00", // 带毫秒和时区
				"2006-01-02T15:04:05.000+08:00", // 带毫秒的中国时区
				// 标准格式
				"2006-01-02 15:04:05",
				"2006-01-02",
			}
			for _, format := range formats {
				if t, err := time.Parse(format, val); err == nil {
					return t
				}
			}
			// 尝试使用RFC3339格式（Go标准）
			if t, err := time.Parse(time.RFC3339, val); err == nil {
				return t
			}
			// 尝试解析Unix时间戳字符串
			if i, err := strconv.ParseInt(val, 10, 64); err == nil {
				return time.Unix(i, 0)
			}
		case int64:
			return time.Unix(val, 0)
		case int:
			return time.Unix(int64(val), 0)
		case float64:
			return time.Unix(int64(val), 0)
		}
		return time.Time{}
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		record.ID = parseUint32(v)
	}
	if v, ok := data["user_id"]; ok {
		record.UserID = parseUint32(v)
	}
	if v, ok := data["order_id"]; ok {
		record.OrderID = parseString(v)
	}
	if v, ok := data["sex"]; ok {
		record.Sex = parseString(v)
	}
	if v, ok := data["nation"]; ok {
		record.Nation = parseString(v)
	}
	if v, ok := data["born"]; ok {
		record.Born = parseString(v)
	}
	if v, ok := data["address"]; ok {
		record.Address = parseString(v)
	}
	if v, ok := data["id_card"]; ok {
		record.IDCard = parseString(v)
	}
	if v, ok := data["begin_date"]; ok {
		record.BeginDate = parseTime(v)
	}
	if v, ok := data["end_date"]; ok {
		record.EndDate = parseTime(v)
	}
	if v, ok := data["department"]; ok {
		record.Department = parseString(v)
	}
	if v, ok := data["created_at"]; ok {
		record.CreatedAt = parseTime(v)
	}
	if v, ok := data["updated_at"]; ok {
		record.UpdatedAt = parseTime(v)
	}

	return nil
}

// parseDate 解析日期字符串为time.Time
func parseDate(dateStr string) (time.Time, error) {
	if dateStr == "" {
		return time.Time{}, nil
	}

	// 尝试解析YYYYMMDD格式
	if len(dateStr) == 8 {
		parsedTime, err := time.Parse("20060102", dateStr)
		if err == nil {
			return parsedTime, nil
		}
	}

	// 尝试其他常见格式
	formats := []string{
		"2006-01-02",
		"2006/01/02",
		"2006.01.02",
	}

	for _, format := range formats {
		if parsedTime, err := time.Parse(format, dateStr); err == nil {
			return parsedTime, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析日期格式: %s", dateStr)
}

// GetOCRRecordByUserID 根据用户ID获取OCR记录
func (s *OCRIdentityRecordService) GetOCRRecordByUserID(userID uint32) (*OCRIdentityRecord, error) {
	if userID <= 0 {
		return nil, fmt.Errorf("用户ID不能为空")
	}

	data, err := DB().Table("ocr_identity_records").
		Where("user_id", userID).
		OrderBy("created_at DESC").
		First()
	if err != nil {
		return nil, fmt.Errorf("查询OCR记录失败: %v", err)
	}

	// 检查是否找到记录
	if len(data) == 0 {
		return nil, nil // 记录不存在时返回 nil, nil
	}

	var record OCRIdentityRecord
	if err := mapToOCRRecord(data, &record); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &record, nil
}

// GetOCRRecordByIDCard 根据身份证号获取OCR记录
func (s *OCRIdentityRecordService) GetOCRRecordByIDCard(idCard string) (*OCRIdentityRecord, error) {
	if idCard == "" {
		return nil, fmt.Errorf("身份证号不能为空")
	}

	data, err := DB().Table("ocr_identity_records").
		Where("id_card", idCard).
		OrderBy("created_at DESC").
		First()
	if err != nil {
		return nil, fmt.Errorf("查询OCR记录失败: %v", err)
	}

	// 检查是否找到记录
	if len(data) == 0 {
		return nil, nil // 记录不存在时返回 nil, nil
	}

	var record OCRIdentityRecord
	if err := mapToOCRRecord(data, &record); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &record, nil
}

// DeleteOCRRecord 删除OCR记录
func (s *OCRIdentityRecordService) DeleteOCRRecord(id uint32) error {
	if id <= 0 {
		return fmt.Errorf("记录ID不能为空")
	}

	_, err := DB().Table("ocr_identity_records").Where("id", id).Delete()
	if err != nil {
		return fmt.Errorf("删除OCR记录失败: %v", err)
	}

	return nil
}
