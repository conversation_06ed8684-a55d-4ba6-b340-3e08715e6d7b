package contract

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"fincore/app/dianziqian/service"
	"fincore/model"
	"fincore/utils/log"
)

// ContractStatusService 合同状态服务
type ContractStatusService struct {
	contractService *model.ContractService
	logger          *log.Logger
}

// NewContractStatusService 创建合同状态服务实例
func NewContractStatusService(ctx context.Context) *ContractStatusService {
	return &ContractStatusService{
		contractService: model.NewContractService(ctx),
		logger:          log.RegisterModule("contract", "合同模块").WithContext(ctx),
	}
}

// QueryContractStatusRequest 查询合同状态请求
type QueryContractStatusRequest struct {
	ContractNo string `json:"contractNo"`
}

// QueryContractStatusResponse 查询合同状态响应
type QueryContractStatusResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		ContractNo   string         `json:"contractNo"`
		Status       int            `json:"status"` // 合同状态 0：等待签约1：签约中2：已签约3：过期4：拒签6：作废7：撤销
		PageSizeMap  map[string]int `json:"pageSizeMap"`
		Remark       string         `json:"remark"`
		ContractName string         `json:"contractName"`
		ValidityTime string         `json:"validityTime"`
		PreviewUrl   string         `json:"previewUrl"`
		EmbeddedUrl  string         `json:"embeddedUrl"`
		SignUser     []SignUser     `json:"signUser"`
	} `json:"data"`
}

// SignUser 签署用户信息
type SignUser struct {
	SignUrl string `json:"signUrl"`
}

// CheckContractStatusResult 合同状态检查结果
type CheckContractStatusResult struct {
	ContractID     int    `json:"contract_id"`      // 合同ID
	ContractNo     string `json:"contract_no"`      // 合同编号
	CurrentStatus  int    `json:"current_status"`   // 当前状态
	LatestStatus   int    `json:"latest_status"`    // 最新状态
	StatusUpdated  bool   `json:"status_updated"`   // 状态是否有更新
	CanCreateOrder bool   `json:"can_create_order"` // 是否可以创建订单
	Message        string `json:"message"`          // 状态消息
}

// CheckContractStatusForOrder 检查合同状态用于订单创建
func (s *ContractStatusService) CheckContractStatusForOrder(userID int, productID int) (*CheckContractStatusResult, error) {
	log.Info("开始检查合同状态，用户ID: %d, 产品ID: %d", userID, productID)

	// 1. 根据用户ID和产品ID查询合同
	contract, err := s.contractService.GetContractByUserIDAndProductID(userID, productID)
	if err != nil {
		s.logger.Errorf("查询合同失败: %v", err)
		return nil, fmt.Errorf("查询合同失败: %v", err)
	}

	if contract == nil {
		s.logger.Errorf("用户 %d 产品 %d 没有找到合同", userID, productID)
		return &CheckContractStatusResult{
			CanCreateOrder: false,
			Message:        "未找到相关合同，请先签署合同",
		}, nil
	}

	result := &CheckContractStatusResult{
		ContractID:    contract.ID,
		ContractNo:    contract.ContractID,
		CurrentStatus: contract.ContractStatus,
		LatestStatus:  contract.ContractStatus,
		StatusUpdated: false,
	}

	// 2. 如果合同状态为签署中（未签署），查询最新状态
	if contract.ContractStatus == model.ContractStatusUnsigned {
		s.logger.Infof("合同状态为未签署，查询最新状态，合同编号: %s", contract.ContractID)

		latestStatus, err := s.queryLatestContractStatus(contract.ContractID)
		if err != nil {
			s.logger.Errorf("查询最新合同状态失败: %v", err)
			// 查询失败不影响主流程，使用当前状态
			result.Message = fmt.Sprintf("查询最新状态失败: %v", err)
		} else {
			result.LatestStatus = latestStatus

			// 3. 如果状态有更新，同步到数据库
			if latestStatus != contract.ContractStatus {
				s.logger.Infof("合同状态有更新，从 %d 更新为 %d", contract.ContractStatus, latestStatus)

				localStatus := model.ConvertThirdPartyStatusToLocal(latestStatus)
				err = s.contractService.UpdateContractStatus(contract.ID, localStatus)
				if err != nil {
					s.logger.Errorf("同步合同状态失败: %v", err)
					result.Message = fmt.Sprintf("同步合同状态失败: %v", err)
				} else {
					result.StatusUpdated = true
					result.CurrentStatus = localStatus
					s.logger.Infof("合同状态同步成功，合同ID: %d, 新状态: %d", contract.ID, localStatus)
				}
			}
		}
	}

	// 4. 判断是否可以创建订单（只有已签约状态才能创建订单）
	finalStatus := result.CurrentStatus
	if result.StatusUpdated {
		finalStatus = result.CurrentStatus
	}

	if finalStatus == model.ContractStatusSigned {
		result.CanCreateOrder = true
		result.Message = "合同已签约，可以创建订单"
	} else {
		result.CanCreateOrder = false
		switch finalStatus {
		case model.ContractStatusUnsigned:
			result.Message = "合同未签署，请先完成签署"
		case model.ContractStatusVoided:
			result.Message = "合同已作废，无法创建订单"
		default:
			result.Message = "合同状态异常，无法创建订单"
		}
	}

	s.logger.Infof("合同状态检查完成，用户ID: %d, 产品ID: %d, 可创建订单: %v, 消息: %s",
		userID, productID, result.CanCreateOrder, result.Message)

	return result, nil
}

// queryLatestContractStatus 查询最新合同状态（带超时控制）
func (s *ContractStatusService) queryLatestContractStatus(contractNo string) (int, error) {
	// 创建5秒超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用通道来处理超时
	resultChan := make(chan int, 1)
	errorChan := make(chan error, 1)

	go func() {
		status, err := s.callQueryContractStatusAPI(contractNo)
		if err != nil {
			errorChan <- err
			return
		}
		resultChan <- status
	}()

	select {
	case status := <-resultChan:
		return status, nil
	case err := <-errorChan:
		return 0, err
	case <-ctx.Done():
		return 0, fmt.Errorf("查询合同状态超时（5秒）")
	}
}

// callQueryContractStatusAPI 调用第三方合同状态查询接口
func (s *ContractStatusService) callQueryContractStatusAPI(contractNo string) (int, error) {
	// 准备请求体
	req := QueryContractStatusRequest{ContractNo: contractNo}
	reqJson, err := json.Marshal(req)
	if err != nil {
		return 0, fmt.Errorf("序列化请求失败: %v", err)
	}

	s.logger.Infof("调用第三方合同状态查询接口，合同编号: %s", contractNo)

	// 调用第三方接口
	respBytes := service.GetContract(string(reqJson))

	// 解析返回
	var resp QueryContractStatusResponse
	err = json.Unmarshal(respBytes, &resp)
	if err != nil {
		return 0, fmt.Errorf("解析返回失败: %v", err)
	}

	if resp.Code != 0 {
		return 0, fmt.Errorf("第三方接口返回错误: %s", resp.Msg)
	}

	s.logger.Infof("第三方合同状态查询成功，合同编号: %s, 状态: %d", contractNo, resp.Data.Status)

	return resp.Data.Status, nil
}
