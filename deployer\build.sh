#!/bin/bash

# Fincore Build Script
# 编译fincore后端和前端项目到deployer目录
# Author: System Administrator
# Version: 1.0.0

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 目录配置
DEPLOYER_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$DEPLOYER_DIR")"
GO_SRC_DIR="$PROJECT_ROOT/go/src"
VUE_DIR="$PROJECT_ROOT/vue"

# 构建目标目录
BUILD_DIR="$DEPLOYER_DIR/build"
APP_DIR="$BUILD_DIR/app"
BUSINESS_DIR="$BUILD_DIR/business"
UNIAPP_DIR="$BUILD_DIR/uniapp"

# 构建配置
BUILD_TIME=$(date '+%Y-%m-%d %H:%M:%S')
BUILD_VERSION="${BUILD_VERSION:-$(date '+%Y%m%d%H%M%S')}"
BUILD_ENV="${BUILD_ENV:-production}"
SKIP_UNIAPP="${SKIP_UNIAPP:-false}"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $*"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $*"
}

log_header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

# 检查依赖
check_dependencies() {
    log_header "检查构建依赖"
    
    # 检查Go环境
    if ! command -v go >/dev/null 2>&1; then
        log_error "Go未安装或不在PATH中"
        exit 1
    fi
    
    local go_version
    go_version=$(go version | awk '{print $3}')
    log_info "Go版本: $go_version"
    
    # 检查Node.js环境
    if ! command -v node >/dev/null 2>&1; then
        log_error "Node.js未安装或不在PATH中"
        exit 1
    fi
    
    local node_version
    node_version=$(node --version)
    log_info "Node.js版本: $node_version"
    
    # 检查包管理器
    if command -v yarn >/dev/null 2>&1; then
        PACKAGE_MANAGER="yarn"
        local yarn_version
        yarn_version=$(yarn --version)
        log_info "Yarn版本: $yarn_version"
    elif command -v npm >/dev/null 2>&1; then
        PACKAGE_MANAGER="npm"
        local npm_version
        npm_version=$(npm --version)
        log_info "NPM版本: $npm_version"
    else
        log_error "npm或yarn未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 创建构建目录结构
create_build_dirs() {
    log_header "创建构建目录结构"
    
    # 创建主要目录
    mkdir -p "$BUILD_DIR"
    mkdir -p "$APP_DIR"
    mkdir -p "$BUSINESS_DIR"
    mkdir -p "$UNIAPP_DIR"
    
    # 创建应用子目录
    mkdir -p "$APP_DIR/bin"
    mkdir -p "$APP_DIR/config"
    mkdir -p "$APP_DIR/logs"
    mkdir -p "$APP_DIR/runtime"
    mkdir -p "$APP_DIR/static"
    
    log_success "构建目录结构创建完成"
}

# 清理构建目录
clean_build_dirs() {
    log_header "清理构建目录"
    
    if [[ -d "$BUILD_DIR" ]]; then
        rm -rf "$BUILD_DIR"
        log_info "清理构建目录: $BUILD_DIR"
    fi
    
    # 清理Go源码目录的构建产物
    if [[ -f "$GO_SRC_DIR/fincore" ]]; then
        rm -f "$GO_SRC_DIR/fincore"
        log_info "清理Go构建产物: fincore"
    fi
    
    if [[ -f "$GO_SRC_DIR/fincore.exe" ]]; then
        rm -f "$GO_SRC_DIR/fincore.exe"
        log_info "清理Go构建产物: fincore.exe"
    fi
    
    # 清理前端构建产物
    local frontend_dist_dirs=(
        "$VUE_DIR/business/dist"
    )
    
    for dist_dir in "${frontend_dist_dirs[@]}"; do
        if [[ -d "$dist_dir" ]]; then
            rm -rf "$dist_dir"
            log_info "清理前端构建产物: $dist_dir"
        fi
    done
    
    log_success "构建目录清理完成"
}

# 构建Go后端
build_go_backend() {
    log_header "构建Go后端"
    
    cd "$GO_SRC_DIR"
    
    # 设置Go构建环境变量
    export CGO_ENABLED=0
    export GOOS=linux
    export GOARCH=amd64
    
    # 构建参数
    local ldflags="-s -w"
    ldflags="$ldflags -X 'main.BuildTime=$BUILD_TIME'"
    ldflags="$ldflags -X 'main.BuildVersion=$BUILD_VERSION'"
    ldflags="$ldflags -X 'main.BuildEnv=$BUILD_ENV'"
    
    log_info "开始编译fincore..."
    log_info "构建环境: GOOS=$GOOS GOARCH=$GOARCH"
    log_info "构建版本: $BUILD_VERSION"
    log_info "构建时间: $BUILD_TIME"
    
    # 下载依赖
    if [[ -f "go.mod" ]]; then
        log_info "下载Go模块依赖..."
        go mod download
        go mod tidy
    fi
    
    # 编译
    if go build -ldflags "$ldflags" -o fincore .; then
        log_success "fincore编译成功"
    else
        log_error "fincore编译失败"
        exit 1
    fi
    
    # 检查编译产物
    if [[ -f "fincore" ]]; then
        local file_size
        file_size=$(du -h fincore | cut -f1)
        log_info "编译产物大小: $file_size"
    else
        log_error "编译产物不存在"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 构建Vue Business前端
build_vue_business() {
    log_header "构建Vue Business前端"
    
    local business_dir="$VUE_DIR/business"
    
    if [[ ! -d "$business_dir" ]]; then
        log_warn "Business前端目录不存在: $business_dir"
        return 0
    fi
    
    cd "$business_dir"
    
    # 检查package.json
    if [[ ! -f "package.json" ]]; then
        log_error "package.json不存在: $business_dir"
        exit 1
    fi
    
    # 安装依赖
    log_info "安装Business前端依赖..."
    if [[ "$PACKAGE_MANAGER" == "yarn" ]]; then
        if ! yarn install --frozen-lockfile; then
            log_error "Business前端依赖安装失败"
            exit 1
        fi
    else
        if ! npm ci; then
            log_error "Business前端依赖安装失败"
            exit 1
        fi
    fi
    
    # 构建
    log_info "构建Business前端..."
    if [[ "$PACKAGE_MANAGER" == "yarn" ]]; then
        if ! yarn build; then
            log_error "Business前端构建失败"
            exit 1
        fi
    else
        if ! npm run build; then
            log_error "Business前端构建失败"
            exit 1
        fi
    fi
    
    # 检查构建产物
    if [[ -d "dist" ]]; then
        local dist_size
        dist_size=$(du -sh dist | cut -f1)
        log_success "Business前端构建成功，大小: $dist_size"
    else
        log_error "Business前端构建产物不存在"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
}

# 处理UniApp前端（暂时使用手动拷贝方式）
build_vue_uniapp() {
    log_header "处理UniApp前端"

    if [[ "$SKIP_UNIAPP" == "true" ]]; then
        log_info "跳过UniApp处理 (SKIP_UNIAPP=true)"
        return 0
    fi

    local uniapp_dir="$VUE_DIR/uniapp"

    if [[ ! -d "$uniapp_dir" ]]; then
        log_warn "UniApp前端目录不存在: $uniapp_dir"
        return 0
    fi

    log_info "当前使用手动拷贝方式处理UniApp前端"
    log_info "UniApp源码目录: $uniapp_dir"

    # 检查是否有手动编译的产物
    # 优先使用HBuilderX编译目录
    local manual_dist_dirs=(
        "$uniapp_dir/unpackage/dist/build/web"
        "$uniapp_dir/unpackage/dist/build/h5"
        "$uniapp_dir/unpackage/dist/dev/h5"
        "$uniapp_dir/dist"
        "$uniapp_dir/build"
        "$uniapp_dir/output"
    )

    local found_manual_dist=false
    local manual_dist_path=""

    for dist_dir in "${manual_dist_dirs[@]}"; do
        if [[ -d "$dist_dir" ]] && [[ -n "$(ls -A "$dist_dir" 2>/dev/null)" ]]; then
            local dist_size
            dist_size=$(du -sh "$dist_dir" | cut -f1)
            log_success "发现手动编译的UniApp产物: $dist_dir (大小: $dist_size)"
            found_manual_dist=true
            manual_dist_path="$dist_dir"
            break
        fi
    done

    if [[ "$found_manual_dist" == "false" ]]; then
        log_warn "未发现UniApp编译产物"
        log_info "请手动编译UniApp并将产物放在以下目录之一："
        for dist_dir in "${manual_dist_dirs[@]}"; do
            log_info "  - $dist_dir"
        done
        log_info "或者设置 SKIP_UNIAPP=true 跳过UniApp处理"

        # 不退出，继续其他组件的构建
        return 0
    fi

    log_success "找到UniApp手动编译产物: $manual_dist_path"

    # TODO: 后续研究出自动编译方法后，可以在这里添加自动编译逻辑
    # 预留函数位置：build_uniapp_auto()
}

# 预留函数：UniApp自动编译（待实现）
build_uniapp_auto() {
    log_header "UniApp自动编译（待实现）"

    local uniapp_dir="$VUE_DIR/uniapp"
    cd "$uniapp_dir"

    # TODO: 实现自动编译逻辑
    # 1. 检查环境和依赖
    # 2. 安装npm依赖
    # 3. 执行构建命令
    # 4. 验证构建产物

    log_warn "UniApp自动编译功能尚未实现"
    log_info "当前请使用手动编译方式："
    log_info "1. 使用HBuilderX打开 $uniapp_dir"
    log_info "2. 选择发行 -> H5-手机版"
    log_info "3. 编译完成后产物会在 unpackage/dist/build/web/ 目录"

    cd "$PROJECT_ROOT"
    return 1
}

# 拷贝文件到构建目录
copy_build_files() {
    log_header "拷贝文件到构建目录"
    
    # 拷贝Go后端
    log_info "拷贝Go后端文件..."
    
    # 拷贝fincore二进制文件
    if [[ -f "$GO_SRC_DIR/fincore" ]]; then
        cp "$GO_SRC_DIR/fincore" "$APP_DIR/bin/"
        chmod +x "$APP_DIR/bin/fincore"
        log_success "拷贝fincore二进制文件"
    else
        log_error "fincore二进制文件不存在"
        exit 1
    fi
    
    # 拷贝Go资源文件
    if [[ -d "$GO_SRC_DIR/resource" ]]; then
        cp -r "$GO_SRC_DIR/resource"/* "$APP_DIR/resource/"
        log_success "拷贝Go配置文件"
    else
        log_warn "Go资源文件目录不存在"
    fi
    
    # 拷贝已有的Go bin目录内容（如果存在）
    local go_bin_dir="$PROJECT_ROOT/go/bin"
    if [[ -d "$go_bin_dir" ]]; then
        # 拷贝配置文件
        if [[ -f "$go_bin_dir/resource/config.yml" ]]; then
            cp "$go_bin_dir/resource/config.yml" "$APP_DIR/config/"
            log_success "拷贝现有配置文件"
        fi
        
        # 拷贝其他必要文件
        local files_to_copy=("mysql_root_access.sh")
        for file in "${files_to_copy[@]}"; do
            if [[ -f "$go_bin_dir/$file" ]]; then
                cp "$go_bin_dir/$file" "$APP_DIR/bin/"
                chmod +x "$APP_DIR/bin/$file"
                log_success "拷贝文件: $file"
            fi
        done
    fi
    
    # 拷贝Vue Business前端
    log_info "拷贝Vue Business前端文件..."
    if [[ -d "$VUE_DIR/business/dist" ]]; then
        cp -r "$VUE_DIR/business/dist"/* "$BUSINESS_DIR/"
        log_success "拷贝Business前端文件"
    else
        log_warn "Business前端构建产物不存在"
    fi
    
    # 拷贝UniApp前端（手动编译产物）
    log_info "拷贝UniApp前端文件..."

    if [[ "$SKIP_UNIAPP" == "true" ]]; then
        log_info "跳过UniApp拷贝 (SKIP_UNIAPP=true)"
        mkdir -p "$UNIAPP_DIR"
        echo "# UniApp已跳过" > "$UNIAPP_DIR/README.txt"
    else
        local uniapp_dist_dirs=(
            "$VUE_DIR/uniapp/unpackage/dist/build/web"
            "$VUE_DIR/uniapp/unpackage/dist/build/h5"
            "$VUE_DIR/uniapp/unpackage/dist/dev/h5"
            "$VUE_DIR/uniapp/dist"
            "$VUE_DIR/uniapp/build"
            "$VUE_DIR/uniapp/output"
        )

        local uniapp_copied=false
        for dist_dir in "${uniapp_dist_dirs[@]}"; do
            if [[ -d "$dist_dir" ]] && [[ -n "$(ls -A "$dist_dir" 2>/dev/null)" ]]; then
                # 确保目标目录存在
                mkdir -p "$UNIAPP_DIR"

                # 拷贝文件
                cp -r "$dist_dir"/* "$UNIAPP_DIR/"
                local dist_size
                dist_size=$(du -sh "$UNIAPP_DIR" | cut -f1)
                log_success "拷贝UniApp前端文件 (来源: $dist_dir, 大小: $dist_size)"
                uniapp_copied=true
                break
            fi
        done

        if [[ "$uniapp_copied" == "false" ]]; then
            log_warn "UniApp前端构建产物不存在"
            log_info "请手动编译UniApp并将产物放在以下目录之一："
            for dist_dir in "${uniapp_dist_dirs[@]}"; do
                log_info "  - $dist_dir"
            done

            # 创建空目录以保持结构完整
            mkdir -p "$UNIAPP_DIR"
            cat > "$UNIAPP_DIR/README.txt" << EOF
# UniApp构建产物未找到

请手动编译UniApp：
1. 使用HBuilderX打开 $VUE_DIR/uniapp
2. 选择发行 -> H5-手机版
3. 编译完成后产物会在 unpackage/dist/build/web/ 目录
4. 重新运行构建脚本

或者设置 SKIP_UNIAPP=true 跳过UniApp处理
EOF
        fi
    fi
    
    log_success "文件拷贝完成"
}

# 更新Watchdog配置
update_watchdog_config() {
    log_header "更新Watchdog配置"
    
    local services_conf="$DEPLOYER_DIR/watchdog/config/services.conf"
    
    if [[ -f "$services_conf" ]]; then
        # 更新服务配置中的路径
        sed -i.bak \
            -e "s|FINCORE_BINARY_PATH=.*|FINCORE_BINARY_PATH=\"/opt/fincore/build/app/bin/fincore\"|" \
            -e "s|FINCORE_PID_FILE=.*|FINCORE_PID_FILE=\"/opt/fincore/build/app/logs/fincore.pid\"|" \
            -e "s|FINCORE_CONFIG_FILE=.*|FINCORE_CONFIG_FILE=\"/opt/fincore/build/app/config/config.yml\"|" \
            -e "s|FINCORE_LOG_FILE=.*|FINCORE_LOG_FILE=\"/opt/fincore/build/app/logs/app.log\"|" \
            -e "s|FINCORE_WORK_DIR=.*|FINCORE_WORK_DIR=\"/opt/fincore/build/app\"|" \
            "$services_conf"
        
        log_success "更新Watchdog服务配置"
    else
        log_warn "Watchdog服务配置文件不存在"
    fi
    
    # 更新systemd服务文件
    local systemd_service="$DEPLOYER_DIR/watchdog/systemd/fincore.service"
    
    if [[ -f "$systemd_service" ]]; then
        sed -i.bak \
            -e "s|WorkingDirectory=.*|WorkingDirectory=/opt/fincore/build/app|" \
            -e "s|ExecStart=.*|ExecStart=/opt/fincore/build/app/bin/fincore|" \
            -e "s|PIDFile=.*|PIDFile=/opt/fincore/build/app/logs/fincore.pid|" \
            "$systemd_service"
        
        log_success "更新systemd服务配置"
    else
        log_warn "systemd服务配置文件不存在"
    fi
}

# 生成构建信息
generate_build_info() {
    log_header "生成构建信息"
    
    local build_info_file="$BUILD_DIR/build_info.txt"
    
    cat > "$build_info_file" << EOF
Fincore Build Information
========================

Build Time: $BUILD_TIME
Build Version: $BUILD_VERSION
Build Environment: $BUILD_ENV
Build Host: $(hostname)
Build User: $(whoami)

Go Information:
- Go Version: $(go version)
- GOOS: $GOOS
- GOARCH: $GOARCH

Node.js Information:
- Node Version: $(node --version)
- Package Manager: $PACKAGE_MANAGER $(if [[ "$PACKAGE_MANAGER" == "yarn" ]]; then yarn --version; else npm --version; fi)

Build Components:
- Go Backend: $(if [[ -f "$APP_DIR/bin/fincore" ]]; then echo "✓"; else echo "✗"; fi)
- Vue Business Frontend: $(if [[ -d "$BUSINESS_DIR" ]] && [[ -n "$(ls -A "$BUSINESS_DIR" 2>/dev/null)" ]]; then echo "✓"; else echo "✗"; fi)
- UniApp Frontend: $(if [[ -d "$UNIAPP_DIR" ]] && [[ -n "$(ls -A "$UNIAPP_DIR" 2>/dev/null)" ]]; then echo "✓"; else echo "✗"; fi)

File Sizes:
$(if [[ -f "$APP_DIR/bin/fincore" ]]; then echo "- fincore: $(du -h "$APP_DIR/bin/fincore" | cut -f1)"; fi)
$(if [[ -d "$BUSINESS_DIR" ]]; then echo "- business: $(du -sh "$BUSINESS_DIR" | cut -f1)"; fi)
$(if [[ -d "$UNIAPP_DIR" ]]; then echo "- uniapp: $(du -sh "$UNIAPP_DIR" | cut -f1)"; fi)

Total Build Size: $(du -sh "$BUILD_DIR" | cut -f1)

Directory Structure:
$BUILD_DIR/
├── app/                     # Go后端应用
│   ├── bin/fincore          # 主程序
│   ├── config/              # 配置文件
│   └── logs/                # 日志目录
├── business/                # Business前端
└── uniapp/                  # UniApp前端

Deployment:
- Copy entire deployer/ directory to target server
- Run: ./watchdog/scripts/install.sh
- Start: ./watchdog/scripts/start.sh
EOF
    
    log_success "构建信息已生成: $build_info_file"
}

# 验证构建结果
verify_build() {
    log_header "验证构建结果"
    
    local errors=0
    
    # 检查Go后端
    if [[ -f "$APP_DIR/bin/fincore" ]]; then
        if [[ -x "$APP_DIR/bin/fincore" ]]; then
            log_success "Go后端: fincore可执行文件存在"
        else
            log_error "Go后端: fincore文件不可执行"
            ((errors++))
        fi
    else
        log_error "Go后端: fincore文件不存在"
        ((errors++))
    fi
    
    # 检查配置文件
    if [[ -d "$APP_DIR/config" ]]; then
        log_success "配置目录: 存在"
    else
        log_warn "配置目录: 不存在"
    fi
    
    # 检查Business前端
    if [[ -d "$BUSINESS_DIR" ]] && [[ -n "$(ls -A "$BUSINESS_DIR" 2>/dev/null)" ]]; then
        log_success "Business前端: 构建产物存在"
    else
        log_warn "Business前端: 构建产物不存在"
    fi
    
    # 检查UniApp前端
    if [[ -d "$UNIAPP_DIR" ]] && [[ -n "$(ls -A "$UNIAPP_DIR" 2>/dev/null)" ]]; then
        log_success "UniApp前端: 构建产物存在"
    else
        log_warn "UniApp前端: 构建产物不存在"
    fi
    
    # 检查Watchdog系统
    if [[ -d "$DEPLOYER_DIR/watchdog" ]]; then
        log_success "Watchdog系统: 存在"
    else
        log_error "Watchdog系统: 不存在"
        ((errors++))
    fi
    
    if [[ $errors -eq 0 ]]; then
        log_success "构建验证通过"
        return 0
    else
        log_error "构建验证失败，发现 $errors 个错误"
        return 1
    fi
}

# 显示构建摘要
show_build_summary() {
    log_header "构建摘要"
    
    echo "构建完成时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo "构建版本: $BUILD_VERSION"
    echo "构建环境: $BUILD_ENV"
    echo ""
    echo "构建产物位置:"
    echo "- Go后端: $APP_DIR/"
    echo "- Business前端: $BUSINESS_DIR/"
    echo "- UniApp前端: $UNIAPP_DIR/"
    echo "- Watchdog系统: $DEPLOYER_DIR/watchdog/"
    echo ""
    echo "部署说明:"
    echo "1. 将整个deployer目录拷贝到目标服务器"
    echo "2. 在目标服务器上执行: sudo ./watchdog/scripts/install.sh"
    echo "3. 启动服务: ./watchdog/scripts/start.sh"
    echo ""
    echo "目录结构:"
    echo "deployer/"
    echo "├── build/           # 构建产物"
    echo "│   ├── app/         # Go后端"
    echo "│   ├── business/    # Business前端"
    echo "│   └── uniapp/      # UniApp前端"
    echo "└── watchdog/        # 监控系统"
    echo ""
    
    if [[ -f "$BUILD_DIR/build_info.txt" ]]; then
        echo "详细构建信息: $BUILD_DIR/build_info.txt"
    fi
}

# 主构建流程
main() {
    local start_time
    start_time=$(date +%s)
    
    log_header "Fincore项目构建开始"
    echo "构建时间: $BUILD_TIME"
    echo "构建版本: $BUILD_VERSION"
    echo "构建环境: $BUILD_ENV"
    echo "构建目录: $BUILD_DIR"
    echo ""
    
    check_dependencies
    clean_build_dirs
    create_build_dirs
    build_go_backend
    build_vue_business
    build_vue_uniapp
    copy_build_files
    update_watchdog_config
    generate_build_info
    
    if verify_build; then
        local end_time
        end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        log_success "构建完成！耗时: ${duration}秒"
        show_build_summary
    else
        log_error "构建失败！"
        exit 1
    fi
}

# 脚本入口
case "${1:-build}" in
    "build")
        main
        ;;
    "clean")
        clean_build_dirs
        ;;
    "go")
        check_dependencies
        create_build_dirs
        build_go_backend
        copy_build_files
        update_watchdog_config
        ;;
    "vue")
        check_dependencies
        create_build_dirs
        build_vue_business
        build_vue_uniapp
        copy_build_files
        ;;
    "verify")
        verify_build
        ;;
    "help"|"-h"|"--help")
        echo "用法: $0 [build|clean|go|vue|verify|help]"
        echo "  build  - 执行完整构建（默认）"
        echo "  clean  - 清理构建目录"
        echo "  go     - 仅构建Go后端"
        echo "  vue    - 仅构建Vue前端"
        echo "  verify - 验证构建结果"
        echo "  help   - 显示帮助信息"
        echo ""
        echo "环境变量:"
        echo "  BUILD_ENV     - 构建环境 (development|testing|production)"
        echo "  BUILD_VERSION - 构建版本号"
        echo "  SKIP_UNIAPP   - 跳过UniApp构建 (true|false)"
        echo ""
        echo "示例:"
        echo "  SKIP_UNIAPP=true ./build.sh    # 跳过UniApp处理"
        echo "  BUILD_ENV=development ./build.sh # 开发环境构建"
        echo ""
        echo "UniApp处理说明:"
        echo "  当前使用手动编译方式，请先手动编译UniApp："
        echo "  1. 使用HBuilderX打开 vue/uniapp 目录"
        echo "  2. 选择发行 -> H5-手机版"
        echo "  3. 编译产物会在 vue/uniapp/unpackage/dist/build/web/ 目录"
        echo "  4. 构建脚本会自动检测并拷贝编译产物"
        echo ""
        echo "构建产物位置: $BUILD_DIR"
        ;;
    *)
        log_error "未知参数: $1"
        echo "使用 '$0 help' 查看帮助信息"
        exit 1
        ;;
esac
