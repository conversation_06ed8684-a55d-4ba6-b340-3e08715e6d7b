package service

import (
	"encoding/json"
	"errors"
	Config "fincore/app/dianziqian/config"
	"fincore/app/dianziqian/httpUtils"
	"fincore/model"
	"fincore/utils/log"
	"fmt"
	"time"
)

func OCR(dataJson string) (*Config.OCRResponse, error) {
	apiUrl := "/user/ocrIdentify"
	log.Info("OCR--------")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Info("请求失败：%v", err)
		return nil, err
	}
	var result Config.OCRResponse
	err = json.Unmarshal(response, &result)
	if err != nil {
		log.Info("JSON解析失败：%v", err)
		return nil, err
	}
	log.Info("OCR结果：%s", string(response))
	log.Info("---OCR结果---：%+v", result)
	if result.Code != 100000 {
		log.Info("请求失败：%s", result.Message)
		return nil, errors.New("请求失败：" + result.Message)
	}
	return &result, nil
}

// OCRWithSave OCR识别并保存到数据库
func OCRWithSave(dataJson string, userID uint32) (*Config.OCRResponse, error) {
	// 调用OCR识别
	result, err := OCR(dataJson)
	if err != nil {
		return nil, err
	}

	// 创建OCR服务实例
	ocrService := model.NewOCRIdentityRecordService()

	// 构建基础OCR记录
	ocrRecord := &model.OCRIdentityRecord{
		UserID: userID,
	}

	// 根据OCR结果类型填充不同字段
	if result.Data.IDCard != "" {
		// 身份证正面信息
		ocrRecord.OrderID = result.Data.OrderID
		ocrRecord.Sex = result.Data.Sex
		ocrRecord.Nation = result.Data.Nation
		ocrRecord.Born = result.Data.Born
		ocrRecord.Address = result.Data.Address
		ocrRecord.IDCard = result.Data.IDCard
		ocrRecord.Department = result.Data.Department
	} else {
		ocrRecord.Department = result.Data.Department
		// 身份证背面信息 - 处理有效期日期
		if beginDate, err := ParseDate(result.Data.Begin); err == nil {
			ocrRecord.BeginDate = beginDate
		} else {
			log.Error("解析开始日期失败: %v", err)
		}
		if endDate, err := ParseDate(result.Data.End); err == nil {
			ocrRecord.EndDate = endDate
		} else {
			log.Error("解析结束日期失败: %v", err)
		}
	}

	// 保存或更新OCR记录
	if err := ocrService.SaveOrUpdateOCRRecord(ocrRecord); err != nil {
		log.Error("保存OCR记录失败 - 用户ID: %d, 错误: %v", userID, err)
	} else {
		logMsg := fmt.Sprintf("OCR记录保存成功 - 用户ID: %d", userID)
		if ocrRecord.IDCard != "" {
			logMsg += fmt.Sprintf(", 身份证号: %s", ocrRecord.IDCard)
		}
		log.Info("%s", logMsg)
	}

	return result, nil
}

// parseDate 解析日期字符串为time.Time
func ParseDate(dateStr string) (time.Time, error) {
	if dateStr == "" {
		return time.Time{}, nil
	}

	// 尝试解析YYYYMMDD格式
	if len(dateStr) == 8 {
		parsedTime, err := time.Parse("20060102", dateStr)
		if err == nil {
			return parsedTime, nil
		}
	}

	// 尝试其他常见格式
	formats := []string{
		"2006-01-02",
		"2006/01/02",
		"2006.01.02",
	}

	for _, format := range formats {
		if parsedTime, err := time.Parse(format, dateStr); err == nil {
			return parsedTime, nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析日期格式: %s", dateStr)
}

func FaceAuth(dataJson string) ([]byte, error) {
	apiUrl := "/auth/person/face"
	log.Info("Authface--------")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Info("请求失败：%v", err)
	}
	var result Config.FaceAuthResponse
	err = json.Unmarshal(response, &result)
	if err != nil {
		log.Info("JSON解析失败：%v", err)
		return nil, err
	}
	if result.Code != 100000 {
		log.Info("请求失败：%s", result.Message)
		return nil, errors.New("请求失败：" + result.Message)
	}
	return response, nil
}

func CheckFaceResult(dataJson string) ([]byte, error) {
	apiUrl := "/user/faceResult"
	log.Info("CheckFaceResult--------")
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Info("请求失败：%v", err)
	}
	var result Config.CheckResponse
	err = json.Unmarshal(response, &result)
	if err != nil {
		log.Info("JSON解析失败：%v", err)
		return nil, err
	}
	if result.Code != 100000 {
		log.Info("请求失败：%s", result.Message)
		return nil, errors.New("请求失败：" + result.Message)
	}
	return response, nil
}

func FacePhoto(dataJson string) ([]byte, error) {
	apiUrl := "/auth/pull/facePhoto"
	log.Info("FacePhoto--------")
	log.Info("dataJson: %s", dataJson)
	response, err := httpUtils.SendRequest(apiUrl, dataJson, nil)
	if err != nil {
		log.Info("请求失败：", err)
		return nil, err
	}
	var result Config.FacePhotoResponse
	err = json.Unmarshal(response, &result)
	if err != nil {
		log.Info("JSON解析失败：", err)
		return nil, err
	}
	if result.Code != 100000 {
		log.Info("请求失败：", result.Message)
		return nil, errors.New("请求失败：" + result.Message)
	}
	return response, nil
}
