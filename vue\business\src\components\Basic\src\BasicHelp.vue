<script lang="tsx">
  import type { CSSProperties, PropType } from 'vue';
  import { defineComponent, computed, unref } from 'vue';
  import { Tooltip } from '@arco-design/web-vue';
  import {IconExclamationCircle  } from '@arco-design/web-vue/es/icon';
  import { getPopupContainer } from '/@/utils';
  import { isString } from '/@/utils/is';
  import { getSlot } from '/@/utils/helper/tsxHelper';

  const props = {
    /**
     * Help text max-width
     * @default: 600px
     */
    maxWidth: { type: String, default: '600px' },
    /**
     * Whether to display the serial number
     * @default: false
     */
    showIndex: { type: Boolean },
    /**
     * Help text font color
     * @default: #ffffff
     */
    color: { type: String, default: '#ffffff' },
    /**
     * Help text font size
     * @default: 14px
     */
    fontSize: { type: String, default: '14px' },
    /**
     * Help text list
     */
    placement: { type: String, default: 'right' },
    /**
     * Help text list
     */
    text: { type: [Array, String] as PropType<string[] | string> },
  };

  export default defineComponent({
    name: 'BasicHelp',
    components: { Tooltip },
    props,
    setup(props, { slots }) {
      const prefixCls= "basic-help";

      const getTooltipStyle = computed(
        (): CSSProperties => ({ color: props.color, fontSize: props.fontSize }),
      );

      const getOverlayStyle = computed((): CSSProperties => ({ maxWidth: props.maxWidth }));

      function renderTitle() {
        const textList = props.text;
        if (isString(textList)) {
          return textList;
        }
        return "--提示错误--";
      }

      return () => {
        return (
          <Tooltip
            content={renderTitle()}
            autoAdjustOverflow={true}
            overlayStyle={unref(getOverlayStyle)}
            placement={props.placement as 'right'}
            getPopupContainer={() => getPopupContainer()}
          >
            <span class={prefixCls}>{getSlot(slots) || <IconExclamationCircle  />}</span>
          </Tooltip>
        );
      };
    },
  });
</script>
<style lang="less">
  .basic-help {
    display: inline-block;
    margin-left: 6px;
    font-size: 14px;
    color: var(--color-neutral-4);
    cursor: pointer;

    &:hover {
      color: var(--color-neutral-6);
    }

    &__wrap {
      p {
        margin-bottom: 0;
      }
    }
  }
</style>
