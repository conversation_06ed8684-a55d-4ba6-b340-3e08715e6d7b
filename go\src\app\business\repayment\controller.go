package repayment

import (
	"fmt"
	"reflect"
	"strconv"

	"fincore/app/business/order"
	"fincore/model"
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

type Manager struct{}

func init() {
	manager := Manager{}
	gf.Register(&manager, reflect.TypeOf(manager).PkgPath())
}

// ManualWithhold 管理员手动代扣
func (m *Manager) ManualWithhold(ctx *gin.Context) {
	// 1. 验证管理员权限
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		results.Failed(ctx, "用户未登录", "")
		return
	}

	// 2. 参数绑定和验证
	var req ManualWithholdRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数绑定失败", err.Error())
		return
	}

	// 3. 参数验证
	schema := GetManualWithholdSchema()
	validator := jsonschema.NewValidator(*schema)

	// 将结构体转换为map进行验证
	reqMap := map[string]interface{}{
		"bill_id":       req.BillID,
		"bank_card_id":  req.BankCardID,
		"amount":        req.Amount,
		"remark":        req.Remark,
		"withhold_type": req.WithholdType,
	}

	validationResult := validator.Validate(reqMap)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}
	billID := int(validationResult.Data["bill_id"].(int))
	// 4. 通过bill_id查询订单并进行权限检查
	_, err := m.getOrderByBillIDAndCheckPermission(ctx, billID)
	if err != nil {
		return
	}

	// 4. 调用服务层处理业务逻辑
	service := NewPaymentService(ctx)
	result, err := service.ManualWithhold(ctx, req.BillID, req.BankCardID, req.Amount, req.Remark, req.WithholdType, int(claim.ID))
	if err != nil {
		results.Failed(ctx, "手动代扣失败", err.Error())
		return
	}

	results.Success(ctx, "手动代扣成功", result, nil)
}

// PaymentCallback 处理支付回调
func (m *Manager) PaymentCallback(ctx *gin.Context) {
	// 绑定回调参数
	var req PaymentCallbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.JSON(400, gin.H{"error": "参数绑定失败: " + err.Error()})
		return
	}

	// 调用业务层处理回调
	businessService := NewPaymentService(ctx)
	if err := businessService.HandlePaymentCallback(&req); err != nil {
		ctx.JSON(500, gin.H{"error": "处理支付回调失败: " + err.Error()})
		return
	}

	// 返回成功响应（第三方支付要求）
	ctx.JSON(200, gin.H{"code": "0000", "message": "success"})
}

// QueryPaymentStatus 查询支付状态
func (m *Manager) GetPaymentStatus(ctx *gin.Context) {
	// 1. 验证管理员权限
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		results.Failed(ctx, "用户未登录", "")
		return
	}

	// 2. 获取交易流水号
	transactionNo := ctx.Query("transaction_no")
	if transactionNo == "" {
		results.Failed(ctx, "参数错误", "交易流水号不能为空")
		return
	}

	// 3. 调用业务层查询支付状态
	businessService := NewPaymentService(ctx)
	response, err := businessService.QueryPaymentStatus(transactionNo)
	if err != nil {
		results.Failed(ctx, "查询支付状态失败", err.Error())
		return
	}

	// 4. 返回成功响应
	results.Success(ctx, "查询支付状态成功", response, nil)
}

// GetSubmittedAmounts 通过账单ID查询已提交的担保费和资管费
func (m *Manager) GetSubmittedAmounts(ctx *gin.Context) {
	// 1. 验证用户权限
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		results.Failed(ctx, "用户未登录", "")
		return
	}

	// 2. 获取账单ID参数
	billIDStr := ctx.Query("bill_id")
	if billIDStr == "" {
		results.Failed(ctx, "参数错误", "账单ID不能为空")
		return
	}

	// 3. 转换账单ID为整数
	billID, err := strconv.Atoi(billIDStr)
	if err != nil {
		results.Failed(ctx, "参数错误", "账单ID格式不正确")
		return
	}

	// 4. 调用服务层查询已提交金额
	service := NewPaymentService(ctx)
	response, err := service.GetSubmittedAmountsByBillID(billID)
	if err != nil {
		results.Failed(ctx, "查询已提交金额失败", err.Error())
		return
	}
	//todo 这里需要加一个管理员权限 刷新流水

	// 5. 返回成功响应
	results.Success(ctx, "查询已提交金额成功", response, nil)
}

// getOrderByBillIDAndCheckPermission 通过账单ID查询订单并进行权限检查
func (m *Manager) getOrderByBillIDAndCheckPermission(ctx *gin.Context, billID int) (*model.BusinessLoanOrders, error) {
	// 1. 验证用户权限
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		results.Failed(ctx, "用户未登录", "")
		return nil, fmt.Errorf("用户未登录")
	}

	// 1. 通过bill_id查询账单信息获取order_id
	billService := model.NewBusinessRepaymentBillsService(ctx)
	bill, err := billService.GetBillByID(billID)
	if err != nil {
		results.Failed(ctx, "查询账单失败", err.Error())
		return nil, err
	}
	if bill == nil {
		results.Failed(ctx, "账单不存在", nil)
		return nil, fmt.Errorf("账单不存在")
	}

	// 2. 通过order_id查询订单信息
	orderService := model.NewBusinessLoanOrdersService(ctx)
	orderInfo, err := orderService.GetOrderByID(bill.OrderID)
	if err != nil {
		results.Failed(ctx, "查询订单失败", err.Error())
		return nil, err
	}
	if orderInfo == nil {
		results.Failed(ctx, "订单不存在", nil)
		return nil, fmt.Errorf("订单不存在")
	}

	// 3. 进行订单操作权限检查
	_, _, err = order.CheckOrderOperationPermissionByUserInfo(claim, orderInfo)
	if err != nil {
		results.Failed(ctx, "权限检查失败", err.Error())
		return nil, err
	}

	return nil, nil
}
