package payment

import (
	"encoding/base64"
	"fmt"
	"math/rand"
	"time"
)

// GenerateRefundOrderNo 生成退款订单号
// 格式: R + 时间戳 + 随机数
// 例如: R158315313614859403
func GenerateRefundOrderNo() string {
	timestamp := time.Now().UnixNano() / 1000000 // 毫秒时间戳
	randomNum := rand.Intn(1000)                 // 3位随机数
	return fmt.Sprintf("R%d%03d", timestamp, randomNum)
}

// GenerateRefundTransactionNo 生成退款流水号
// 格式: RF + 时间戳 + 随机数
// 例如: RF1703123456789000001
func GenerateRefundTransactionNo() string {
	timestamp := time.Now().UnixNano()
	randomNum := rand.Intn(1000000)
	return fmt.Sprintf("RF%d%06d", timestamp, randomNum)
}

// EncodeNotifyURL 对回调URL进行base64编码
func EncodeNotifyURL(url string) string {
	return base64.StdEncoding.EncodeToString([]byte(url))
}

// DecodeNotifyURL 对回调URL进行base64解码
func DecodeNotifyURL(encodedURL string) (string, error) {
	decoded, err := base64.StdEncoding.DecodeString(encodedURL)
	if err != nil {
		return "", fmt.Errorf("解码回调URL失败: %v", err)
	}
	return string(decoded), nil
}
