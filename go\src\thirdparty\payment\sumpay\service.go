package sumpay

import (
	"context"
	"crypto/aes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fincore/global"
	"fincore/utils/config"
	"fincore/utils/crypto"
	"fincore/utils/log"
	"fmt"
	"net"
	"net/http"
	"os"
	"time"

	"github.com/go-resty/resty/v2"
)

// SumpayService 商盟支付服务接口
type SumpayServiceInterface interface {
	Execute(request *Request, result interface{}) error
	Sign(request *SignRequest) (*SignResponse, error)                                  //一键签约
	ValidSMS(request *ValidSMSRequest) (*ValidSMSResponse, error)                      //一键签约短信验证
	Pay(request *PayRequest) (*PayResponse, error)                                     //一键支付
	QueryPayOrder(request *QueryPayOrderRequest) (*QueryPayOrderResponse, error)       //支付订单查询
	TransferToCard(request DisbursementParams) (map[string]interface{}, error)         //转账到卡
	QueryDisbursement(request DisbursementQueryParams) (map[string]interface{}, error) //查询代付结果
	Refund(request *RefundRequest) (map[string]interface{}, error)                     //退款
	RefundSearch(request *RefundSearchRequest) (map[string]interface{}, error)         //退款查询
}

// SumpayServiceImpl 商盟支付服务实现
type SumpayServiceImpl struct {
	config   *config.Config
	client   *resty.Client
	signUtil *crypto.SignUtil
	aesUtil  *crypto.AESUtil
	logger   *log.Logger
}

var SumpayService SumpayServiceInterface

func WithContext(ctx context.Context) func(*SumpayServiceImpl) {
	return func(service *SumpayServiceImpl) {
		service.logger = service.logger.WithContext(ctx)
	}
}

// NewSumpayService 创建商盟支付服务实例
func NewSumpayService(opts ...func(*SumpayServiceImpl)) (SumpayServiceInterface, error) {
	if SumpayService == nil {

		cfg := &global.App.Config
		if cfg.SumPay.AppId == "" || cfg.SumPay.MerNo == "" {
			return nil, fmt.Errorf("商盟支付配置不完整")
		}
		client := resty.New()
		// 创建自定义的HTTP传输层，解决连接问题
		transport := &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second, // 连接超时
				KeepAlive: 30 * time.Second, // 保持连接
			}).DialContext,
			TLSHandshakeTimeout:   10 * time.Second, // TLS握手超时
			ResponseHeaderTimeout: 10 * time.Second, // 响应头超时
			ExpectContinueTimeout: 1 * time.Second,  // Expect: 100-continue超时
			MaxIdleConns:          100,              // 最大空闲连接数
			MaxIdleConnsPerHost:   10,               // 每个主机最大空闲连接数
			IdleConnTimeout:       90 * time.Second, // 空闲连接超时
		}

		// 配置TLS
		if cfg.SumPay.SkipTLSVerify {
			transport.TLSClientConfig = &tls.Config{
				InsecureSkipVerify: true,
			}
		} else {
			transport.TLSClientConfig = &tls.Config{
				MinVersion: tls.VersionTLS12,
			}
		}

		// 设置自定义传输层
		client.SetTransport(transport)

		// 配置HTTP客户端超时时间
		timeout := time.Duration(cfg.SumPay.RequestTimeout) * time.Second
		if timeout == 0 {
			timeout = 60 * time.Second // 增加默认超时时间
		}
		client.SetTimeout(timeout)

		// 配置重试
		retryCount := cfg.SumPay.RetryCount
		if retryCount == 0 {
			retryCount = 3
		}
		client.SetRetryCount(retryCount)

		retryWaitTime := time.Duration(cfg.SumPay.RetryWaitTime) * time.Second
		if retryWaitTime == 0 {
			retryWaitTime = 5 * time.Second
		}
		client.SetRetryWaitTime(retryWaitTime)

		// 设置User-Agent
		client.SetHeader("User-Agent", "SMDemo-Go/1.0")

		SumpayService := &SumpayServiceImpl{
			config:   cfg,
			client:   client,
			signUtil: crypto.NewSignUtil(),
			aesUtil:  crypto.NewAESUtil(),
			logger:   log.RegisterModule("sumpay", "商盟支付"),
		}

		for _, opt := range opts {
			opt(SumpayService)
		}

		return SumpayService, nil

	}
	return SumpayService, nil

}

// Sign
//
//	@Description: 签约 第一步
//	@receiver s
//	@param request
//	@return *SignResponse
//	@return error
func (s *SumpayServiceImpl) Sign(request *SignRequest) (*SignResponse, error) {
	req, err := NewGateWayRequest(request)
	req.AesEncodedWords = GetSignSendMessageAesEncodedWords()
	req.Base64EncodedWords = GetSignSendMessageBase64EncodedWords()
	req.CharsetChangeWords = GetSignSendMessageCharsetChangeWords()
	var signResponse SignResponse
	err = s.Execute(req, &signResponse)
	if err != nil {
		return nil, err
	}
	return &signResponse, nil
}

// ValidSMS
//
//	@Description: 一键签约短信验证
//	@receiver s
func (s *SumpayServiceImpl) ValidSMS(request *ValidSMSRequest) (*ValidSMSResponse, error) {
	req, err := NewGateWayRequest(request)
	req.AesEncodedWords = GetSignSendMessageAesEncodedWords()
	req.Base64EncodedWords = GetSignSendMessageBase64EncodedWords()
	req.CharsetChangeWords = GetSignSendMessageCharsetChangeWords()
	if err != nil {
		return nil, err
	}
	var validSMSResponse ValidSMSResponse
	err = s.Execute(req, &validSMSResponse)
	if err != nil {
		return nil, err
	}
	return &validSMSResponse, nil
}

// Pay 一键支付(还款)
func (s *SumpayServiceImpl) Pay(request *PayRequest) (*PayResponse, error) {
	req, err := NewGateWayRequest(request)
	if err != nil {
		return nil, err
	}
	var payResponse PayResponse
	err = s.Execute(req, &payResponse)
	if err != nil {
		return nil, err
	}
	return &payResponse, nil
}

// QueryPayOrder
//
//	@Description: 查询支付结果
//	@receiver s
//	@param request
//	@return *QueryPayOrderResponse
//	@return error
func (s *SumpayServiceImpl) QueryPayOrder(request *QueryPayOrderRequest) (*QueryPayOrderResponse, error) {
	req, err := NewGateWayRequest(request)
	if err != nil {
		return nil, err
	}
	var queryPayOrderResponse QueryPayOrderResponse
	err = s.Execute(req, &queryPayOrderResponse)
	if err != nil {
		return nil, err
	}
	return &queryPayOrderResponse, nil
}

// H5QuickPay
//
//	@Description: 用户主动支付
//	@receiver s
//	@param request
//	@return *H5QuickPayResponse
//	@return error
func (s *SumpayServiceImpl) H5QuickPay(request *H5QuickPayRequest) (*H5QuickPayResponse, error) {
	req, err := NewGateWayRequest(request)
	if err != nil {
		return nil, err
	}
	var h5QuickPayResponse H5QuickPayResponse
	err = s.Execute(req, &h5QuickPayResponse)
	if err != nil {
		return nil, err
	}
	return &h5QuickPayResponse, nil
}

const (
	FeeAmount   = "0"                 // 手续费
	NeedNotify  = "0"                 // 是否需要通知  (通过定时任务去补偿刷新状态)
	CardType    = "0"                 // 卡类型 0:借记卡;1:信用卡
	IDType      = "1"                 // 证件类型 1:身份证;2:护照;3:士兵证/军官证/警官证;4:港澳居民往来内地通行证;5:营业执照号
	IDValidDate = "20250708-20250708" // 证件有效期 格式: yyyyMMdd-yyyyMMdd
)

// TransferToCard 转账到卡
func (s *SumpayServiceImpl) TransferToCard(request DisbursementParams) (map[string]interface{}, error) {

	request.NotifyURL = s.config.SumPay.NotifyUrl
	request.NeedNotify = NeedNotify
	request.FeeAmount = FeeAmount
	request.IDType = IDType
	request.CardType = CardType
	request.IDValidDate = IDValidDate // todo 修改数据库真实有效期

	sumpayReq := NewPrivateAgentPayRequest(request)
	req, err := NewGateWayRequest(sumpayReq)
	req.AesEncodedWords = GetPrivateAgentPayAesEncodedWords()
	req.Base64EncodedWords = GetPrivateAgentPayBase64EncodedWords()
	req.CharsetChangeWords = GetPrivateAgentPayCharsetChangeWords()

	if err != nil {
		return nil, err
	}
	var response map[string]interface{}
	err = s.Execute(req, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// QueryDisbursement 查询放款结果
func (s *SumpayServiceImpl) QueryDisbursement(request DisbursementQueryParams) (map[string]interface{}, error) {
	sumpayReq := NewDisbursementQueryRequest(s.config.SumPay.AppId, s.config.SumPay.MerNo, request)
	req, err := NewGateWayRequest(sumpayReq)
	req.AesEncodedWords = GetDisbursementQueryAesEncodedWords()
	req.Base64EncodedWords = GetDisbursementQueryBase64EncodedWords()
	req.CharsetChangeWords = GetDisbursementQueryCharsetChangeWords()

	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = s.Execute(req, &response)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// Refund 退款
func (s *SumpayServiceImpl) Refund(request *RefundRequest) (map[string]interface{}, error) {
	req, err := NewGateWayRequest(request)
	req.AesEncodedWords = GetRefundAesEncodedWords()
	req.Base64EncodedWords = GetRefundBase64EncodedWords()
	req.CharsetChangeWords = GetRefundCharsetChangeWords()

	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = s.Execute(req, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// RefundSearch 退款查询
func (s *SumpayServiceImpl) RefundSearch(request *RefundSearchRequest) (map[string]interface{}, error) {
	req, err := NewGateWayRequest(request)

	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = s.Execute(req, &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

// Execute
//
//	@Description: 请求商盟
//	@receiver s
//	@param request
//	@param result	返回结构体
//	@return error
func (s *SumpayServiceImpl) Execute(request *Request, result interface{}) error {
	s.logger.WithFields(
		log.String("request", fmt.Sprintf("%+v", request.Content)),
	).Info("开始执行商盟支付请求")

	// 1. 准备请求数据
	requestData, err := s.prepareRequestData(request)
	if err != nil {
		s.logger.WithFields(
			log.String("error", err.Error()),
		).Error("准备请求数据失败")
		return fmt.Errorf("准备请求数据失败: %v", err)
	}

	// 2. 对请求数据进行签名
	signedData, err := s.signRequestData(requestData, request)
	if err != nil {
		s.logger.WithFields(
			log.String("error", err.Error()),
		).Error("签名请求数据失败")
		return fmt.Errorf("签名请求数据失败: %v", err)
	}

	// 3. 发送HTTP请求
	response, err := s.sendHTTPRequest(request.URL, signedData, request)
	if err != nil {
		s.logger.WithFields(
			log.String("error", err.Error()),
		).Error("发送HTTP请求失败")
		return fmt.Errorf("发送HTTP请求失败: %v", err)
	}

	// 4. 处理响应数据
	resp, err := s.processResponse(response)
	if err != nil {
		s.logger.WithFields(
			log.String("error", err.Error()),
		).Error("处理响应数据失败")
		return fmt.Errorf("处理响应数据失败: %v", err)
	}
	respStr, err := json.Marshal(resp)
	if err != nil {
		return fmt.Errorf("处理响应数据失败: %v", err)
	}
	if err = json.Unmarshal(respStr, result); err != nil {
		return fmt.Errorf("处理响应数据失败: %v", err)
	}
	s.logger.WithFields(
		log.String("response", fmt.Sprintf("%+v", resp)),
	).Info("商盟支付请求执行成功")
	return nil
}

// prepareRequestData 准备请求数据
func (s *SumpayServiceImpl) prepareRequestData(request *Request) (map[string]interface{}, error) {
	var requestData map[string]interface{}

	// 解析JSON内容
	if err := json.Unmarshal([]byte(request.Content), &requestData); err != nil {
		return nil, fmt.Errorf("解析请求内容失败: %v", err)
	}

	// 确保所有值都是字符串格式（商盟支付API要求）
	// 剔除空值元素，否则会导致签名失败
	stringData := make(map[string]interface{})
	for key, value := range requestData {
		if value != nil && value != "" {
			stringData[key] = fmt.Sprintf("%v", value)
		}
	}

	// 处理AES编码字段
	if len(request.AesEncodedWords) > 0 {
		if err := s.processAESEncoding(stringData, request.AesEncodedWords); err != nil {
			return nil, fmt.Errorf("AES编码处理失败: %v", err)
		}
	}

	// 处理Base64编码字段
	if len(request.Base64EncodedWords) > 0 {
		if err := s.processBase64Encoding(stringData, request.Base64EncodedWords); err != nil {
			return nil, fmt.Errorf("Base64编码处理失败: %v", err)
		}
	}

	return stringData, nil
}

// signRequestData 对请求数据进行签名
func (s *SumpayServiceImpl) signRequestData(requestData map[string]interface{}, request *Request) (map[string]interface{}, error) {
	// 将请求数据转换为字符串map
	stringParams := make(map[string]string)
	for key, value := range requestData {
		stringParams[key] = fmt.Sprintf("%v", value)
	}

	// 使用我们实现的签名字符串生成功能
	signString, err := crypto.GenerateSignString(stringParams)
	if err != nil {
		return nil, fmt.Errorf("生成签名字符串失败: %v", err)
	}

	s.logger.WithFields(
		log.String("sign_string", signString),
	).Debug("待签名字符串")

	// 对签名字符串进行签名
	signature, err := s.signUtil.SignMessage(
		signString,
		request.PrivateKeyPath,
		request.Password,
		request.Charset,
		request.CertType,
	)
	if err != nil {
		return nil, fmt.Errorf("签名失败: %v", err)
	}

	// 添加签名到请求数据
	requestData["sign"] = signature
	requestData["sign_type"] = request.CertType

	return requestData, nil
}

// sendHTTPRequest 发送HTTP请求
func (s *SumpayServiceImpl) sendHTTPRequest(url string, data map[string]interface{}, request *Request) (*resty.Response, error) {
	req := s.client.R().
		SetHeader("Charset", request.Charset)

	// 商盟支付API统一使用form-data格式
	formData := make(map[string]string)
	for key, value := range data {
		formData[key] = fmt.Sprintf("%v", value)
	}

	req.SetFormData(formData)

	s.logger.WithFields(
		log.String("url", url),
		log.String("form_data", fmt.Sprintf("%+v", formData)),
	).Info("发送请求到")

	response, err := req.Post(url)
	if err != nil {
		return nil, err
	}

	s.logger.WithFields(
		log.Int("status_code", response.StatusCode()),
		log.String("response_body", string(response.Body())),
	).Info("收到响应")

	if response.StatusCode() != 200 {
		return nil, fmt.Errorf("HTTP请求失败，状态码: %d，响应: %s", response.StatusCode(), string(response.Body()))
	}

	return response, nil
}

// processResponse 处理响应数据
func (s *SumpayServiceImpl) processResponse(response *resty.Response) (map[string]string, error) {
	var responseData map[string]interface{}

	// 解析响应JSON
	if err := json.Unmarshal(response.Body(), &responseData); err != nil {
		return nil, fmt.Errorf("解析响应数据失败: %v", err)
	}
	//if responseData["resp_code"] != RESP_SUCCESS {
	//	return nil, errors.New(responseData["resp_msg"].(string))
	//}
	// 转换为string map
	result := make(map[string]string)
	for key, value := range responseData {
		result[key] = fmt.Sprintf("%v", value)
	}

	return result, nil
}

// processAESEncoding 处理AES编码字段
func (s *SumpayServiceImpl) processAESEncoding(data map[string]interface{}, fields []string) error {
	// 检查是否有需要加密的字段
	hasFieldsToEncrypt := false
	for _, field := range fields {
		if _, exists := data[field]; exists {
			hasFieldsToEncrypt = true
			break
		}
	}

	if !hasFieldsToEncrypt {
		return nil
	}

	// 生成AES-256密钥（与Java版本一致）
	aesKey := make([]byte, 32) // 32字节 = 256位
	if _, err := rand.Read(aesKey); err != nil {
		return fmt.Errorf("生成AES密钥失败: %v", err)
	}

	// 将密钥转换为Base64字符串（与Java版本一致）
	aesKeyBase64 := base64.StdEncoding.EncodeToString(aesKey)
	fmt.Printf("生成AES-256密钥: %s\n", aesKeyBase64)

	// 对指定字段进行AES加密
	for _, field := range fields {
		if value, exists := data[field]; exists {
			valueStr := fmt.Sprintf("%v", value)

			// 使用与Java版本相同的AES加密方式
			encryptedValue, err := s.aesEncryptJavaCompatible(valueStr, aesKeyBase64)
			if err != nil {
				return fmt.Errorf("AES加密字段%s失败: %v", field, err)
			}

			data[field] = encryptedValue
			fmt.Printf("AES加密 %s: %s -> %s\n", field, valueStr, encryptedValue)
		}
	}

	// 使用RSA公钥加密AES密钥（Base64字符串）
	encryptedAESKey, err := s.rsaEncryptAESKeyString(aesKeyBase64)
	if err != nil {
		return fmt.Errorf("RSA加密AES密钥失败: %v", err)
	}

	// 将加密的AES密钥添加到请求中
	data["aes_key"] = encryptedAESKey
	fmt.Printf("添加aes_key字段: %s\n", encryptedAESKey)

	return nil
}

// processBase64Encoding 处理Base64编码字段
func (s *SumpayServiceImpl) processBase64Encoding(data map[string]interface{}, fields []string) error {
	// 对指定字段进行Base64编码
	for _, field := range fields {
		if value, exists := data[field]; exists {
			valueStr := fmt.Sprintf("%v", value)
			data[field] = base64.StdEncoding.EncodeToString([]byte(valueStr))
		}
	}
	return nil
}

// aesEncryptJavaCompatible 与Java版本兼容的AES加密
// 使用AES/ECB/PKCS5Padding模式，密钥为Base64编码的字符串
func (s *SumpayServiceImpl) aesEncryptJavaCompatible(plaintext, aesKeyBase64 string) (string, error) {
	// 解码Base64密钥
	aesKey, err := base64.StdEncoding.DecodeString(aesKeyBase64)
	if err != nil {
		return "", fmt.Errorf("解码AES密钥失败: %v", err)
	}

	// 创建AES cipher
	block, err := aes.NewCipher(aesKey)
	if err != nil {
		return "", err
	}

	// 将字符串转换为UTF-8字节数组
	plaintextBytes := []byte(plaintext)

	// PKCS5/PKCS7 padding
	padding := aes.BlockSize - len(plaintextBytes)%aes.BlockSize
	padtext := make([]byte, len(plaintextBytes)+padding)
	copy(padtext, plaintextBytes)
	for i := len(plaintextBytes); i < len(padtext); i++ {
		padtext[i] = byte(padding)
	}

	// ECB模式加密（不需要IV）
	ciphertext := make([]byte, len(padtext))
	for i := 0; i < len(padtext); i += aes.BlockSize {
		block.Encrypt(ciphertext[i:i+aes.BlockSize], padtext[i:i+aes.BlockSize])
	}

	// 返回Base64编码的结果
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// rsaEncryptAESKeyString 使用RSA公钥加密AES密钥字符串
func (s *SumpayServiceImpl) rsaEncryptAESKeyString(aesKeyBase64 string) (string, error) {
	// 读取公钥文件
	keyData, err := os.ReadFile(s.config.SumPay.Cert.PublicKeyPath)
	if err != nil {
		return "", fmt.Errorf("读取公钥文件失败: %v", err)
	}

	// 解析公钥
	var publicKey *rsa.PublicKey

	// 尝试解析PEM格式
	block, _ := pem.Decode(keyData)
	if block != nil {
		cert, err := x509.ParseCertificate(block.Bytes)
		if err != nil {
			return "", fmt.Errorf("解析证书失败: %v", err)
		}

		var ok bool
		publicKey, ok = cert.PublicKey.(*rsa.PublicKey)
		if !ok {
			return "", fmt.Errorf("公钥不是RSA格式")
		}
	} else {
		// 尝试解析DER格式
		cert, err := x509.ParseCertificate(keyData)
		if err != nil {
			return "", fmt.Errorf("解析DER证书失败: %v", err)
		}

		var ok bool
		publicKey, ok = cert.PublicKey.(*rsa.PublicKey)
		if !ok {
			return "", fmt.Errorf("公钥不是RSA格式")
		}
	}

	// 使用RSA公钥加密AES密钥字符串（转换为字节数组）
	encryptedKey, err := rsa.EncryptPKCS1v15(rand.Reader, publicKey, []byte(aesKeyBase64))
	if err != nil {
		return "", fmt.Errorf("RSA加密失败: %v", err)
	}

	// 返回Base64编码的加密密钥
	return base64.StdEncoding.EncodeToString(encryptedKey), nil
}
