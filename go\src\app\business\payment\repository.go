package payment

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"fincore/model"
	"fincore/utils/gform"
)

// PaymentRepository 支付数据访问层
type PaymentRepository struct {
	transactionModel *model.BusinessPaymentTransactionsService
	billModel        *model.BusinessRepaymentBillsService
	orderModel       *model.BusinessLoanOrdersService
}

// NewPaymentRepository 创建支付数据访问层实例
func NewPaymentRepository(ctx context.Context) *PaymentRepository {
	return &PaymentRepository{
		transactionModel: model.NewBusinessPaymentTransactionsService(ctx),
		billModel:        model.NewBusinessRepaymentBillsService(ctx),
		orderModel:       model.NewBusinessLoanOrdersService(ctx),
	}
}

// GetTransactionByID 根据ID获取交易流水
func (r *PaymentRepository) GetTransactionByID(transactionID int) (*model.BusinessPaymentTransactions, error) {
	return r.transactionModel.GetTransactionByID(transactionID)
}

// GetTransactionByNo 根据流水号获取交易流水
func (r *PaymentRepository) GetTransactionByNo(transactionNo string) (*model.BusinessPaymentTransactions, error) {
	return r.transactionModel.GetTransactionByNo(transactionNo)
}

// GetTransactionByRelatedTransactionNo 根据关联流水号获取交易流水
func (r *PaymentRepository) GetTransactionByRelatedTransactionNo(params model.TransactionCondition) ([]model.BusinessPaymentTransactions, error) {
	transactions, err := r.transactionModel.GetTransactionsByConditions(params)
	if err != nil {
		return nil, err
	}
	return transactions, nil
}

// CreateTransaction 创建交易流水
func (r *PaymentRepository) CreateTransaction(transaction *model.BusinessPaymentTransactions) error {
	return r.transactionModel.CreateTransaction(transaction)
}

// UpdateTransactionStatus 更新交易状态
func (r *PaymentRepository) UpdateTransactionStatus(where model.UpdateTransactionStatusResultWhere, updateMap map[string]interface{}) error {
	return r.transactionModel.UpdateTransactionStatus(nil, where, updateMap)
}

// GetBillByID 根据ID获取账单
func (r *PaymentRepository) GetBillByID(billID int) (*model.BusinessRepaymentBills, error) {
	return r.billModel.GetBillByID(billID)
}

// GetOrderByID 根据ID获取订单
func (r *PaymentRepository) GetOrderByID(orderID int) (*model.BusinessLoanOrders, error) {
	return r.orderModel.GetOrderByID(orderID)
}

// CreateOperationLog 创建操作日志
func (r *PaymentRepository) CreateOperationLog(orderID int, operatorID int, operatorName, action, details string) error {
	operationLog := &model.BusinessOrderOperationLogs{
		OrderID:      orderID,
		OperatorID:   operatorID,
		OperatorName: operatorName,
		Action:       action,
		Details:      details,
	}

	return model.NewBusinessOrderOperationLogsService().CreateLog(operationLog)
}

// AddBillRefundAmount 增加账单累计退款金额
func (r *PaymentRepository) AddBillRefundAmount(billID int, refundAmount float64) error {
	return r.AddBillRefundAmountWithTx(nil, billID, refundAmount)
}

// AddBillRefundAmountWithTx 增加账单累计退款金额（支持事务）
func (r *PaymentRepository) AddBillRefundAmountWithTx(tx gform.IOrm, billID int, refundAmount float64) error {
	db := model.DB()
	if tx != nil {
		db = tx
	}

	// 先获取当前累计退款金额
	currentAmount, err := r.GetBillRefundAmount(billID)
	if err != nil {
		return fmt.Errorf("获取当前累计退款金额失败: %v", err)
	}

	// 计算新的累计退款金额
	newAmount := currentAmount + refundAmount

	// 更新累计退款金额
	_, err = db.Table("business_repayment_bills").
		Where("id", billID).
		Update(map[string]interface{}{
			"total_refund_amount": newAmount,
		})

	if err != nil {
		return fmt.Errorf("增加账单累计退款金额失败: %v", err)
	}

	return nil
}

// SubtractBillRefundAmount 减少账单累计退款金额（撤回退款）
func (r *PaymentRepository) SubtractBillRefundAmount(billID int, refundAmount float64) error {
	return r.SubtractBillRefundAmountWithTx(nil, billID, refundAmount)
}

// SubtractBillRefundAmountWithTx 减少账单累计退款金额（支持事务）
func (r *PaymentRepository) SubtractBillRefundAmountWithTx(tx gform.IOrm, billID int, refundAmount float64) error {
	db := model.DB()
	if tx != nil {
		db = tx
	}

	// 先获取当前累计退款金额
	currentAmount, err := r.GetBillRefundAmount(billID)
	if err != nil {
		return fmt.Errorf("获取当前累计退款金额失败: %v", err)
	}

	// 计算新的累计退款金额，确保不会小于0
	newAmount := currentAmount - refundAmount
	if newAmount < 0 {
		newAmount = 0
	}

	// 更新累计退款金额
	_, err = db.Table("business_repayment_bills").
		Where("id", billID).
		Update(map[string]interface{}{
			"total_refund_amount": newAmount,
		})

	if err != nil {
		return fmt.Errorf("减少账单累计退款金额失败: %v", err)
	}

	return nil
}

// GetBillRefundAmount 获取账单累计退款金额
func (r *PaymentRepository) GetBillRefundAmount(billID int) (float64, error) {
	data, err := model.DB().Table("business_repayment_bills").
		Where("id", billID).
		First()

	if err != nil {
		return 0, fmt.Errorf("查询账单累计退款金额失败: %v", err)
	}

	if len(data) == 0 {
		return 0, fmt.Errorf("账单不存在")
	}

	refundAmount, ok := data["total_refund_amount"].(float64)
	if !ok {
		// 尝试从string转换
		if str, isStr := data["total_refund_amount"].(string); isStr {
			if amount, parseErr := strconv.ParseFloat(str, 64); parseErr == nil {
				return amount, nil
			}
		}
		return 0, fmt.Errorf("累计退款金额数据类型错误")
	}

	return refundAmount, nil
}

// SoftDeleteTransaction 软删除流水记录
func (r *PaymentRepository) SoftDeleteTransaction(tx gform.IOrm, transactionNo string, deletedAt time.Time) error {
	var handler gform.IOrm
	if tx != nil {
		handler = tx.Table("business_payment_transactions")
	} else {
		handler = model.DB().Table("business_payment_transactions")
	}

	_, err := handler.Where("transaction_no", transactionNo).Update(map[string]interface{}{
		"deleted_at": deletedAt,
	})
	if err != nil {
		return fmt.Errorf("软删除流水记录失败: %v", err)
	}

	return nil
}
