//添加表单框架
.addFormbox{
    height: 100%;
    display: flex;
    //右侧边导航
    .tabs-header{
      width: 122px;
      text-align: center;
      border-right: 1px solid var(--color-neutral-3);
      position: relative;
      .tabs-nav-wrap{
        margin-right:-1px;
      }
      .tap_item{
        width: 100%;
        height: 65px;
        line-height: 65px;
        cursor: pointer;
        user-select: none;
        border: 1px solid transparent;
        border-left: 0;
        border-right-color: var(--color-neutral-3);
        transition: border-bottom .2s ease,border-top .2s ease;
        margin-top: -1px;
        &.item_active{
          color: #557ce1;
          border-right-color: var(--color-bg-3);
          border-top-color: var(--color-neutral-3);
          border-bottom-color: var(--color-neutral-3);
        }
        &:hover{
          color: #557ce1;
        }
      }
      //选中标志
      .tabs-bar{
        width: 6px;
        background-color: #557ce1;
        position: absolute;
        left: 0;
        transition: top .2s ease;
      }
    }
    //右边内容区
    .tabs-content{
      flex:1;
      overflow: hidden;
      .content_box{
        width: 100%;
        .besecontent{
          padding: 25px 20px 10px 10px;
        }
      }
    }
  }