import { defHttp } from '@/utils/http';

// 基础API响应接口
export interface BaseResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 产品规则接口
export interface ProductRule {
  id: number;
  rule_name: string;
  loan_amount: number;
  loan_period: number;
  total_periods: number;
  guarantee_fee: number;
  annual_interest_rate: number;
  other_fees: number;
  rule_category: string;
  repayment_method: string;
  created_at: string;
  updated_at: string;
}

// 放款规则接口 - 包含产品规则信息
export interface LoanRule {
  rule_id: number;
  min_risk_score: number;
  max_risk_score: number;
  product_rule?: ProductRule; // 关联的产品规则信息
}

// 渠道查询参数接口
export interface ChannelQueryParams {
  channel_name?: string;
  channel_code?: string;
  channel_status?: number;
  channel_usage?: number;
  mobile?: string;
  page?: number;
  pageSize?: number;
}

// 渠道信息接口
export interface ChannelItem {
  id: number;
  channel_name: string;
  channel_code: string;
  domain: string;
  mobile: string;
  password: string;
  channel_status: number;
  channel_usage: number;
  uv: number;
  register_count: number;
  real_name_count: number;
  download_count: number;
  machine_review_count: number;
  transaction_success_count: number;
  retention_rate: number;
  operation: string;
  loan_limits: string;
  loan_rules?: LoanRule[]; // 解析后的放款规则
  risk_control_1_limit: number;
  risk_control_1_upper: number;
  risk_control_2_limit: number;
  risk_control_2_upper: number;
  point_amount_1: number;
  point_amount_2: number;
  total_amount: number;
  remark: string;
  create_time: string;
  update_time: string;
}

// 渠道创建/更新参数接口
export interface ChannelFormData {
  channel_name: string;
  channel_code?: string; // 可选，不填则自动生成
  domain?: string;
  mobile?: string;
  password?: string;
  channel_status?: number;
  channel_usage?: number;
  uv?: number;
  register_count?: number;
  real_name_count?: number;
  download_count?: number;
  machine_review_count?: number;
  transaction_success_count?: number;
  retention_rate?: number;
  operation?: string;
  loan_rule_id?: number; // 前端使用的放款规则ID
  loan_rules?: string; // 后端期望的放款规则JSON字符串
  risk_control_1_limit?: number;
  risk_control_1_upper?: number;
  risk_control_2_limit?: number;
  risk_control_2_upper?: number;
  point_amount_1?: number;
  point_amount_2?: number;
  total_amount?: number;
  remark?: string;
}

// 渠道列表响应接口（HTTP拦截器处理后的数据结构）
export interface ChannelListResponse {
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  data: ChannelItem[];
}

// 渠道选项接口
export interface ChannelOption {
  id: number;
  name: string;
  value: number;  // 对应id，用于表单选择
  label: string;  // 对应name，用于显示
}

// 渠道选项响应接口（后端返回格式）
export interface ChannelOptionResponse {
  value: number;
  label: string;
  id: number;
  name: string;
}

// 生成邀请链接参数接口
export interface InvitationParams {
  channel_id: number;
}

// 邀请链接生成结果接口
export interface InvitationResult {
  invitation_url: string;
  qr_code_base64: string;
  channel_code: string;
}

// 渠道状态检查结果接口
export interface ChannelStatusResult {
  channel_id: number;
  channel_code: string;
  channel_status: number;
  channel_name: string;
}

enum Api {
  GetChannelList = '/channel/channelcontroller/listChannels',
  GetChannelDetail = '/channel/channelcontroller/getChannelDetail',
  CreateChannel = '/channel/channelcontroller/createChannel',
  UpdateChannel = '/channel/channelcontroller/updateChannel',
  DeleteChannel = '/channel/channelcontroller/deleteChannel',
  GetChannelOptions = '/channel/channelcontroller/getChannelOptions',
  GetChannelLoanRules = '/channel/channelcontroller/getChannelLoanRules',
  UpdateChannelLoanRules = '/channel/channelcontroller/updateChannelLoanRules',
  GenerateChannelCode = '/channel/channelcontroller/generateChannelCode',
  GenerateChannelInvitation = '/channel/channelcontroller/generateChannelInvitation',
  CheckChannelStatus = '/channel/channelcontroller/checkChannelStatus',
  GetProductRules = '/channel/channelcontroller/getProductRules',
}

/**
 * 获取渠道列表
 */
export function getChannelList(data: ChannelQueryParams) {
  return defHttp.post<ChannelListResponse>({
    url: Api.GetChannelList,
    data
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取渠道详情
 */
export function getChannelDetail(id: number) {
  return defHttp.get<BaseResponse<ChannelItem>>({
    url: `${Api.GetChannelDetail}/${id}`
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 创建渠道
 */
export function createChannel(data: ChannelFormData) {
  return defHttp.post<BaseResponse<ChannelItem>>({
    url: Api.CreateChannel,
    data
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 更新渠道
 */
export function updateChannel(id: number, data: ChannelFormData) {
  return defHttp.post<BaseResponse<ChannelItem>>({
    url: Api.UpdateChannel,
    data: {
      id,
      ...data
    }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 删除渠道
 */
export function deleteChannel(id: number) {
  return defHttp.delete<BaseResponse<any>>({
    url: Api.DeleteChannel,
    data: { id }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取渠道选项（下拉框用）
 */
export function getChannelOptions() {
  console.log('开始请求渠道选项API:', Api.GetChannelOptions);
  
  // 添加完整URL日志
  const apiUrl = window.globalConfig.Root_url || '';
  console.log('完整API URL:', `${apiUrl}${Api.GetChannelOptions}`);
  
  return defHttp.get<ApiResponseData<ChannelOptionResponse[]>>({
    url: Api.GetChannelOptions
  }, {
    errorMessageMode: 'message',
    // 重要：禁用响应转换，保留原始响应格式
    isTransformResponse: false,
    retryRequest: {
      isOpenRetry: false,
      count: 0,
      waitTime: 100
    }
  }).catch(error => {
    console.error('获取渠道选项失败:', error);
    // 返回空对象和空数组作为默认值
    return {
      code: -1,
      message: '请求失败',
      data: [],
      exdata: null,
      time: Date.now()
    };
  });
}

/**
 * 获取渠道放款规则
 */
export function getChannelLoanRules(id: number) {
  return defHttp.get<BaseResponse<LoanRule[]>>({
    url: `${Api.GetChannelLoanRules}/${id}`
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 更新渠道放款规则
 */
export function updateChannelLoanRules(id: number, loanRules: LoanRule[]) {
  return defHttp.put<BaseResponse<any>>({
    url: `${Api.UpdateChannelLoanRules}/${id}`,
    data: {
      loan_rules: loanRules
    }
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 生成新的渠道编码
 */
export function generateChannelCode() {
  return defHttp.post<BaseResponse<{ channel_code: string }>>({
    url: Api.GenerateChannelCode
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 生成渠道邀请链接和二维码
 */
export function generateChannelInvitation(data: InvitationParams) {
  return defHttp.post<BaseResponse<InvitationResult>>({
    url: Api.GenerateChannelInvitation,
    data
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 检查渠道状态
 */
export function checkChannelStatus(channelCode: string) {
  return defHttp.get<BaseResponse<ChannelStatusResult>>({
    url: `${Api.CheckChannelStatus}/${channelCode}`
  }, {
    errorMessageMode: 'message'
  });
}

/**
 * 获取产品规则列表
 */
export function getProductRules() {
  return defHttp.get<BaseResponse<ProductRule[]>>({
    url: Api.GetProductRules
  }, {
    errorMessageMode: 'message'
  });
}