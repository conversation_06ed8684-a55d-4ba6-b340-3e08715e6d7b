package log

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type ContextKey string

const (
	RequestIDKey ContextKey = "request_id"
	LoggerKey    ContextKey = "logger"
)

// Logger 日志记录器接口
type Logger struct {
	logger *zap.Logger
	fields []Field
	module string
	ctx    context.Context
}

// NewLogger 创建新的日志记录器
func NewLogger(module string) *Logger {
	return &Logger{
		logger: GetManager().GetLogger(module),
		fields: make([]Field, 0),
		module: module,
	}
}

// WithModule 设置模块
func (l *Logger) WithModule(module string) *Logger {
	return &Logger{
		logger: GetManager().GetLogger(module),
		fields: l.fields,
		module: module,
	}
}

// WithField 添加字段（优化版本）
func (l *Logger) WithField(key string, value interface{}) *Logger {
	// 使用预分配的slice避免多次内存分配
	newFields := make([]Field, 0, len(l.fields)+1)
	newFields = append(newFields, l.fields...)
	newFields = append(newFields, Any(key, value))

	return &Logger{
		logger: l.logger,
		fields: newFields,
		module: l.module,
	}
}

// WithFields 添加多个字段（优化版本）
func (l *Logger) WithFields(fields ...Field) *Logger {
	if len(fields) == 0 {
		return l
	}

	// 使用预分配的slice避免多次内存分配
	newFields := make([]Field, 0, len(l.fields)+len(fields))
	newFields = append(newFields, l.fields...)
	newFields = append(newFields, fields...)

	return &Logger{
		logger: l.logger,
		fields: newFields,
		module: l.module,
	}
}

// WithContext 从上下文中获取信息（优化版本）
func (l *Logger) WithContext(ctx context.Context) *Logger {
	// 从上下文中提取请求ID
	requestID := GetRequestIDFromContext(ctx)
	if requestID == "" {
		return l // 如果没有请求ID，直接返回原logger
	}

	// 使用预分配的slice
	newFields := make([]Field, 0, len(l.fields)+1)
	newFields = append(newFields, l.fields...)
	newFields = append(newFields, RequestID(requestID))

	return &Logger{
		logger: l.logger,
		fields: newFields,
		module: l.module,
		ctx:    ctx,
	}
}

// WithGinContext 从Gin上下文中获取信息
func (l *Logger) WithGinContext(ctx *gin.Context) *Logger {
	newLogger := l.WithContext(ctx.Request.Context())

	// 添加HTTP相关信息
	newLogger.fields = append(newLogger.fields,
		Method(ctx.Request.Method),
		URL(ctx.Request.URL.Path),
		IP(ctx.ClientIP()),
	)

	// 添加用户代理
	if ua := ctx.GetHeader("User-Agent"); ua != "" {
		newLogger.fields = append(newLogger.fields, UserAgent(ua))
	}

	return newLogger
}

// WithRequestID 添加请求ID
func (l *Logger) WithRequestID(requestID string) *Logger {
	return l.WithField("request_id", requestID)
}

// WithUserID 添加用户ID
func (l *Logger) WithUserID(userID interface{}) *Logger {
	return l.WithField("user_id", userID)
}

// WithError 添加错误信息
func (l *Logger) WithError(err error) *Logger {
	if err == nil {
		return l
	}
	return l.WithFields(ErrorField(err))
}

// Debug 调试日志（优化版本）
func (l *Logger) Debug(msg string, fields ...Field) {
	if len(fields) == 0 {
		l.logger.Debug(msg, l.fields...)
	} else {
		allFields := make([]Field, 0, len(l.fields)+len(fields))
		allFields = append(allFields, l.fields...)
		allFields = append(allFields, fields...)
		l.logger.Debug(msg, allFields...)
	}
}

// Info 信息日志（优化版本）
func (l *Logger) Info(msg string, fields ...Field) {
	if len(fields) == 0 {
		l.logger.Info(msg, l.fields...)
	} else {
		allFields := make([]Field, 0, len(l.fields)+len(fields))
		allFields = append(allFields, l.fields...)
		allFields = append(allFields, fields...)
		l.logger.Info(msg, allFields...)
	}
}

// Warn 警告日志（优化版本）
func (l *Logger) Warn(msg string, fields ...Field) {
	if len(fields) == 0 {
		l.logger.Warn(msg, l.fields...)
	} else {
		allFields := make([]Field, 0, len(l.fields)+len(fields))
		allFields = append(allFields, l.fields...)
		allFields = append(allFields, fields...)
		l.logger.Warn(msg, allFields...)
	}
}

// Error 错误日志（优化版本）
func (l *Logger) Error(msg string, fields ...Field) {
	if len(fields) == 0 {
		l.logger.Error(msg, l.fields...)
	} else {
		allFields := make([]Field, 0, len(l.fields)+len(fields))
		allFields = append(allFields, l.fields...)
		allFields = append(allFields, fields...)
		l.logger.Error(msg, allFields...)
	}
}

// Fatal 致命错误日志（优化版本）
func (l *Logger) Fatal(msg string, fields ...Field) {
	if len(fields) == 0 {
		l.logger.Fatal(msg, l.fields...)
	} else {
		allFields := make([]Field, 0, len(l.fields)+len(fields))
		allFields = append(allFields, l.fields...)
		allFields = append(allFields, fields...)
		l.logger.Fatal(msg, allFields...)
	}
}

// Panic 恐慌日志（优化版本）
func (l *Logger) Panic(msg string, fields ...Field) {
	if len(fields) == 0 {
		l.logger.Panic(msg, l.fields...)
	} else {
		allFields := make([]Field, 0, len(l.fields)+len(fields))
		allFields = append(allFields, l.fields...)
		allFields = append(allFields, fields...)
		l.logger.Panic(msg, allFields...)
	}
}

// Debugf 格式化调试日志
func (l *Logger) Debugf(template string, args ...interface{}) {
	template = fmt.Sprintf("request_id: %s, %s", GetRequestIDFromContext(l.ctx), template)
	l.logger.Sugar().Debugf(template, args...)
}

// Infof 格式化信息日志
func (l *Logger) Infof(template string, args ...interface{}) {
	template = fmt.Sprintf("request_id: %s, %s", GetRequestIDFromContext(l.ctx), template)
	l.logger.Sugar().Infof(template, args...)
}

// Warnf 格式化警告日志
func (l *Logger) Warnf(template string, args ...interface{}) {
	template = fmt.Sprintf("request_id: %s, %s", GetRequestIDFromContext(l.ctx), template)
	l.logger.Sugar().Warnf(template, args...)
}

// Errorf 格式化错误日志
func (l *Logger) Errorf(template string, args ...interface{}) {
	template = fmt.Sprintf("request_id: %s, %s", GetRequestIDFromContext(l.ctx), template)
	l.logger.Sugar().Errorf(template, args...)
}

// Fatalf 格式化致命错误日志
func (l *Logger) Fatalf(template string, args ...interface{}) {
	template = fmt.Sprintf("request_id: %s, %s", GetRequestIDFromContext(l.ctx), template)
	l.logger.Sugar().Fatalf(template, args...)
}

// Panicf 格式化恐慌日志
func (l *Logger) Panicf(template string, args ...interface{}) {
	template = fmt.Sprintf("request_id: %s, %s", GetRequestIDFromContext(l.ctx), template)
	l.logger.Sugar().Panicf(template, args...)
}

// GetRequestIDFromContext 从上下文中获取请求ID
func GetRequestIDFromContext(ctx context.Context) string {
	if ctx == nil {
		return ""
	}

	if requestID, ok := ctx.Value(RequestIDKey).(string); ok {
		return requestID
	}

	return ""
}

// SetRequestIDToContext 设置请求ID到上下文
func SetRequestIDToContext(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, RequestIDKey, requestID)
}
