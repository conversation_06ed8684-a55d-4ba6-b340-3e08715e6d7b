package log

import (
	"context"
	"fincore/global"
	"fincore/utils/config"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
)

// 初始化测试环境
func setupTestEnv() {
	// 模拟全局配置
	global.App = &global.Application{
		Config: config.Config{
			Log: config.Log{
				RootDir:    "./test_logs",
				Level:      "debug",
				Format:     "json",
				ShowLine:   true,
				MaxBackups: 3,
				MaxSize:    10,
				MaxAge:     7,
				Compress:   true,
			},
		},
	}

	// 初始化一个简单的 zap logger 用于测试
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger
}

// TestBasicLogging 测试基础日志功能
func TestBasicLogging(t *testing.T) {
	setupTestEnv()

	// 测试向后兼容的日志函数
	Info("这是一条测试信息日志")
	Debug("这是一条测试调试日志")
	Error("这是一条测试错误日志")
	Warn("这是一条测试警告日志")

	// 测试格式化日志
	Info("用户 %s 登录成功", "testuser")
	Error("处理请求失败: %s", "test error")
}

// TestModuleLogging 测试模块化日志
func TestModuleLogging(t *testing.T) {
	setupTestEnv()

	// 测试不同模块的日志器
	customerLogger := Customer()
	customerLogger.Info("客户模块日志测试")

	orderLogger := Order()
	orderLogger.Info("订单模块日志测试")

	bankCardLogger := BankCard()
	bankCardLogger.Info("银行卡模块日志测试")

	// 测试WithModule
	businessLogger := WithModule("business")
	businessLogger.Info("业务模块日志测试")
}

// TestStructuredLogging 测试结构化日志
func TestStructuredLogging(t *testing.T) {
	setupTestEnv()

	// 测试字段化日志
	logger := WithModule("test")
	logger.WithField("user_id", 12345).
		WithField("action", "login").
		WithField("ip", "***********").
		Info("用户登录")

	// 测试多字段日志
	logger.WithFields(
		String("order_id", "ORD123456"),
		Float64("amount", 99.99),
		String("currency", "CNY"),
		Bool("success", true),
	).Info("订单处理完成")

	// 测试错误日志
	err := assert.AnError
	logger.WithError(err).Error("处理订单时发生错误")
}

// TestContextLogging 测试上下文日志
func TestContextLogging(t *testing.T) {
	setupTestEnv()

	// 测试上下文日志
	ctx := context.Background()
	ctx = SetRequestIDToContext(ctx, "req-test-123")

	logger := WithContext(ctx)
	logger.Info("带上下文的日志")

	// 测试请求ID
	logger = WithRequestID("req-456")
	logger.Info("带请求ID的日志")

	// 测试用户ID
	logger = WithUserID(12345)
	logger.Info("带用户ID的日志")
}

// TestGinMiddleware 测试Gin中间件
func TestGinMiddleware(t *testing.T) {
	setupTestEnv()

	gin.SetMode(gin.TestMode)
	r := gin.New()

	// 添加日志中间件
	r.Use(RequestIDMiddleware())
	r.Use(AccessLogMiddleware())
	r.Use(ErrorLogMiddleware())

	// 测试路由
	r.GET("/test", func(c *gin.Context) {
		logger := WithRequestContext(c)
		logger.Info("处理测试请求")
		c.JSON(200, gin.H{"message": "success"})
	})

	// 发送测试请求
	w := httptest.NewRecorder()
	req, _ := http.NewRequest("GET", "/test", nil)
	r.ServeHTTP(w, req)

	assert.Equal(t, 200, w.Code)
	assert.NotEmpty(t, w.Header().Get("X-Request-ID"))
}

// TestLogLevels 测试日志级别
func TestLogLevels(t *testing.T) {
	setupTestEnv()

	logger := WithModule("level_test")

	// 测试不同级别的日志
	logger.Debug("调试日志")
	logger.Info("信息日志")
	logger.Warn("警告日志")
	logger.Error("错误日志")

	// 测试格式化日志
	logger.Debugf("调试日志: %s", "debug")
	logger.Infof("信息日志: %s", "info")
	logger.Warnf("警告日志: %s", "warn")
	logger.Errorf("错误日志: %s", "error")
}

// TestBusinessScenarios 测试业务场景
func TestBusinessScenarios(t *testing.T) {
	setupTestEnv()

	// 订单处理场景
	orderLogger := Order()
	orderLogger.WithFields(
		OrderID("ORD20250717001"),
		UserID(12345),
		Amount(1999.99),
		Currency("CNY"),
	).Info("订单创建成功")

	// 客户管理场景
	customerLogger := Customer()
	customerLogger.WithFields(
		UserID(12345),
		String("action", "register"),
		String("channel", "mobile"),
	).Info("客户注册成功")

	// 银行卡场景
	bankCardLogger := BankCard()
	bankCardLogger.WithFields(
		UserID(12345),
		String("card_number", "****1234"),
		String("bank_name", "工商银行"),
		String("action", "bind"),
	).Info("银行卡绑定成功")
}

// TestPerformance 测试性能
func TestPerformance(t *testing.T) {
	setupTestEnv()

	logger := WithModule("perf_test")

	// 测试大量日志写入性能
	start := time.Now()
	for i := 0; i < 1000; i++ {
		logger.WithFields(
			Int("iteration", i),
			String("test_type", "performance"),
			Time("timestamp", time.Now()),
		).Info("性能测试日志")
	}
	duration := time.Since(start)

	t.Logf("写入1000条日志耗时: %v", duration)
	assert.Less(t, duration, time.Second*5, "日志写入性能应该在5秒内")
}

// TestChainedCalls 测试链式调用
func TestChainedCalls(t *testing.T) {
	setupTestEnv()

	// 测试链式调用
	Order().
		WithRequestID("req-chain-001").
		WithUserID(12345).
		WithField("action", "create").
		WithField("amount", 999.99).
		WithError(assert.AnError).
		Info("链式调用测试")

	// 测试多次链式调用
	logger := Customer().
		WithRequestID("req-chain-002").
		WithUserID(67890)

	logger.WithField("step", "validation").Info("开始验证")
	logger.WithField("step", "processing").Info("开始处理")
	logger.WithField("step", "completion").Info("处理完成")
}

// BenchmarkLogging 基准测试
func BenchmarkLogging(b *testing.B) {
	setupTestEnv()
	logger := WithModule("benchmark")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		logger.WithFields(
			Int("iteration", i),
			String("benchmark", "test"),
		).Info("基准测试日志")
	}
}

// BenchmarkStructuredLogging 结构化日志基准测试
func BenchmarkStructuredLogging(b *testing.B) {
	setupTestEnv()
	logger := Order()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		logger.WithFields(
			OrderID(i),
			UserID(12345),
			Amount(99.99),
			Currency("CNY"),
			String("status", "created"),
		).Info("订单创建")
	}
}
