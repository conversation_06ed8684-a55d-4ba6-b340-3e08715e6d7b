package order

import (
	"context"
	"fmt"
	"time"

	"fincore/utils/pagination"

	"golang.org/x/sync/errgroup"
)

// GetOrderBillInfo 获取订单账单信息
func (s *Service) GetOrderBillInfo(orderNo string) (*OrderBillInfoResponse, error) {
	// 1. 先查询订单基本信息
	order, err := s.repository.GetOrderByOrderNo(orderNo)
	if err != nil {
		s.logger.Errorf("查询订单失败: %v", err)
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	var (
		billList            []BillInfo
		disbursementRecords []DisbursementRecord
	)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	g, _ := errgroup.WithContext(ctx)

	// 查询账单列表
	g.Go(func() error {
		bills, err := s.repository.GetBillListByOrderID(int(order.ID))
		if err != nil {
			return err
		}
		billList = bills
		return nil
	})

	// 查询放款记录
	g.Go(func() error {
		records, err := s.repository.GetDisbursementRecordsByOrderID(int(order.ID))
		if err != nil {
			return err
		}
		disbursementRecords = records
		return nil
	})

	if err := g.Wait(); err != nil {
		s.logger.Errorf("并发查询失败: %v", err)
		return nil, fmt.Errorf("查询数据失败: %v", err)
	}

	return &OrderBillInfoResponse{
		TotalRepayableAmount: fmt.Sprintf("%.2f", float64(order.TotalRepayableAmount)),
		BillList:             billList,
		DisbursementRecords:  disbursementRecords,
	}, nil
}

// GetOrderPaymentRecords 获取订单支付记录列表（分页）
func (s *Service) GetOrderPaymentRecords(orderNo string, page, pageSize int) (*pagination.PaginationResponse, error) {
	// 1. 先查询订单基本信息
	order, err := s.repository.GetOrderByOrderNo(orderNo)
	if err != nil {
		s.logger.Errorf("查询订单失败: %v", err)
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if order == nil {
		return nil, fmt.Errorf("订单不存在")
	}

	// 2. 查询支付记录
	paymentRecords, err := s.repository.GetPaymentRecordsByOrderID(int(order.ID), page, pageSize)
	if err != nil {
		s.logger.Errorf("查询支付记录失败: %v", err)
		return nil, fmt.Errorf("查询支付记录失败: %v", err)
	}

	return paymentRecords, nil
}

// UpdateBillDueDate 修改账单时间
func (s *Service) UpdateBillDueDate(billID int, dueDateStr string) error {
	// 1. 参数验证
	if billID <= 0 {
		return fmt.Errorf("账单ID无效")
	}

	// 2. 解析时间字符串
	dueDate, err := time.Parse("2006-01-02", dueDateStr)
	if err != nil {
		return fmt.Errorf("时间格式错误，请使用 YYYY-MM-DD 格式")
	}

	// 4. 调用Repository层更新数据库
	err = s.repository.UpdateBillDueDate(billID, dueDate)
	if err != nil {
		s.logger.Errorf("更新账单到期时间失败: %v", err)
		return fmt.Errorf("更新账单到期时间失败: %v", err)
	}

	s.logger.Infof("成功更新账单 %d 的到期时间为 %s", billID, dueDateStr)
	return nil
}
