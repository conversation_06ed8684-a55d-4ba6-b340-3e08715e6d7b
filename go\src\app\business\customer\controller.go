package customer

import (
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"fmt"
	"reflect"
	"strconv"

	"github.com/gin-gonic/gin"
)

type CustomerController struct{}

func init() {
	controller := CustomerController{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// ListCustomers 获取业务应用账户列表
func (c *CustomerController) ListCustomers(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetCustomerListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取业务应用账户列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取业务应用账户列表成功", result, nil)
}

// GetCustomerDetail 获取业务应用账户详情
func (c *CustomerController) GetCustomerDetail(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Query("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerDetail(id)
	if err != nil {
		results.Failed(ctx, "获取业务应用账户详情失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取业务应用账户详情成功", result, nil)
}

// UpdateCustomerStatus 更新业务应用账户状态
func (c *CustomerController) UpdateCustomerStatus(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetCustomerStatusUpdateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	idValue, exists := validationResult.Data["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	// 前端传递的是 number 类型，在 Go 中解析为 float64
	floatID, ok := idValue.(float64)
	if !ok {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}
	id := int64(floatID)
	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.UpdateCustomerStatus(id, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "更新业务应用账户状态失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "更新业务应用账户状态成功", nil, nil)
}

// UpdateCustomerRemark 更新业务应用账户备注
func (c *CustomerController) UpdateCustomerRemark(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetCustomerRemarkUpdateSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 从请求体中获取ID
	idValue, exists := validationResult.Data["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	// 前端传递的是 number 类型，在 Go 中解析为 float64
	floatID, ok := idValue.(float64)
	if !ok {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}
	id := int64(floatID)

	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.UpdateCustomerRemark(id, validationResult.Data)
	if err != nil {
		results.Failed(ctx, "更新业务应用账户备注失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "更新业务应用账户备注成功", nil, nil)
}

// GetCustomerOptions 获取业务应用账户筛选选项
func (c *CustomerController) GetCustomerOptions(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerOptions()
	if err != nil {
		results.Failed(ctx, "获取业务应用账户选项失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取业务应用账户选项成功", result, nil)
}

// GetCustomerRemarks 获取业务应用账户备注历史
func (c *CustomerController) GetCustomerRemarks(ctx *gin.Context) {
	// 1. 参数校验
	idStr := ctx.Query("id")
	if idStr == "" {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerDetail(id)
	if err != nil {
		results.Failed(ctx, "获取业务应用账户备注历史失败", err.Error())
		return
	}

	// 构建备注响应
	remarkData := map[string]interface{}{
		"id":         result.Id,
		"userRemark": result.UserRemark,
		"updateTime": result.CreateTime, // 暂时使用创建时间，后续可添加备注更新时间字段
	}

	// 3. 返回结果
	results.Success(ctx, "获取业务应用账户备注历史成功", remarkData, nil)
}

// GetCustomerStatistics 获取业务应用账户统计信息
func (c *CustomerController) GetCustomerStatistics(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetCustomerStatistics()
	if err != nil {
		results.Failed(ctx, "获取业务应用账户统计信息失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取业务应用账户统计信息成功", result, nil)
}

// ExportCustomers 导出业务应用账户数据
func (c *CustomerController) ExportCustomers(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	err := service.ExportCustomers(ctx, nil)
	if err != nil {
		results.Failed(ctx, "导出业务应用账户数据失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "导出业务应用账户数据成功", nil, nil)
}

// ListRepurchaseCustomers 获取复购业务应用账户列表
func (c *CustomerController) ListRepurchaseCustomers(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetRepurchaseCustomerListSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); !ok || str != "" {
			cleanData[k] = v
		}
	}

	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetRepurchaseCustomerList(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "获取复购业务应用账户列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取复购业务应用账户列表成功", result, nil)
}

// SendRepurchaseSMS 发送复购短信
func (c *CustomerController) SendRepurchaseSMS(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetSendRepurchaseSMSSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.SendRepurchaseSMS(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "发送复购短信失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "发送复购短信成功", nil, nil)
}

// RecordRepurchaseAwaken 记录复购唤醒
func (c *CustomerController) RecordRepurchaseAwaken(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	schema := GetRecordRepurchaseAwakenSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 获取当前登录用户信息
	userInfo, exists := ctx.Get("user")
	if !exists {
		results.Failed(ctx, "认证失败", "无法获取用户信息")
		return
	}

	// 将用户信息添加到请求数据中
	if user, ok := userInfo.(*middleware.UserClaims); ok {
		validationResult.Data["operatorId"] = user.ID

		// 优先使用Name，如果为空则使用Username
		operatorName := user.Name
		if operatorName == "" {
			operatorName = user.Username
		}
		validationResult.Data["operatorName"] = operatorName
	} else {
		results.Failed(ctx, "认证失败", "用户信息格式错误")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err := service.RecordRepurchaseAwaken(validationResult.Data)
	if err != nil {
		results.Failed(ctx, "记录复购唤醒失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "记录复购唤醒成功", nil, nil)
}

// GetRepurchaseAwakenRecords 获取复购唤醒记录
func (c *CustomerController) GetRepurchaseAwakenRecords(ctx *gin.Context) {
	// 1. 参数校验
	customerIdStr := ctx.Query("customerId")
	if customerIdStr == "" {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	customerId, err := strconv.ParseInt(customerIdStr, 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetRepurchaseAwakenRecords(customerId)
	if err != nil {
		results.Failed(ctx, "获取复购唤醒记录失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "获取复购唤醒记录成功", result, nil)
}

// GetRepurchaseCustomerOptions 获取复购业务应用账户筛选选项
func (c *CustomerController) GetRepurchaseCustomerOptions(ctx *gin.Context) {
	// 业务逻辑处理
	service := CustomerService{}
	result, err := service.GetRepurchaseCustomerOptions()
	if err != nil {
		results.Failed(ctx, "获取复购业务应用账户选项失败", err.Error())
		return
	}

	// 返回结果
	results.Success(ctx, "获取复购业务应用账户选项成功", result, nil)
}

// UnlockCustomer 解除注销业务应用账户
func (c *CustomerController) UnlockCustomer(ctx *gin.Context) {
	// 1. 参数校验
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	idInterface, exists := requestData["id"]
	if !exists {
		results.Failed(ctx, "参数错误", "业务应用账户ID不能为空")
		return
	}

	id, err := strconv.ParseInt(fmt.Sprintf("%v", idInterface), 10, 64)
	if err != nil {
		results.Failed(ctx, "参数错误", "业务应用账户ID格式无效")
		return
	}

	// 2. 业务逻辑处理
	service := CustomerService{}
	err = service.UnlockCustomer(id)
	if err != nil {
		results.Failed(ctx, "解除注销业务应用账户失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(ctx, "解除注销业务应用账户成功", nil, nil)
}

// UpdateCustomerQuota 修改用户额度
func (c *CustomerController) UpdateCustomerQuota(ctx *gin.Context) {
	// 1. 参数校验
	schema := GetUpdateQuotaSchema()
	validator := jsonschema.NewValidator(schema)

	// 从POST请求体获取数据
	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		results.Failed(ctx, "请求参数解析失败", err.Error())
		return
	}

	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 参数转换
	params := UpdateQuotaParams{
		CustomerID: int(requestData["customerId"].(float64)),
		ProductID:  int(requestData["productId"].(float64)),
		Amount:     requestData["amount"].(float64),
	}

	// 3. 业务逻辑处理
	service := CustomerService{}
	err := service.UpdateCustomerQuota(params)
	if err != nil {
		results.Failed(ctx, "修改客户额度失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(ctx, "修改客户额度成功", nil, nil)
}
