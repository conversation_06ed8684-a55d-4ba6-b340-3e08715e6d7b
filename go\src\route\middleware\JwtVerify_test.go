package middleware

import (
	"fincore/global"
	"fmt"
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// 初始化测试环境
func init() {
	// 设置测试配置
	global.App.Config.App.TokenOutTime = "120" // 设置token过期时间为120分钟
}

// 测试生成token
func TestGenerateToken(t *testing.T) {
	// 创建测试用户信息
	claims := &UserClaims{
		ID:        26,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}

	// 生成token
	tokenStr := GenerateToken(claims)

	// 验证token不为空
	//assert.NotEmpty(t, token, "生成的token不应为空")

	fmt.Println("生成的token: ", tokenStr)
}

// 测试解析token
func TestParseToken(t *testing.T) {
	// 创建测试用户信息
	claims := &UserClaims{
		ID:        1,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}

	// 生成token
	tokenStr := GenerateToken(claims)

	tokenStr = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MjYsImFjY291bnRpZCI6MTAwLCJidXNpbmVzc0lEIjowLCJvcGVuaWQiOiIiLCJuYW1lIjoi5rWL6K-V55So5oi3IiwidXNlcm5hbWUiOiJ0ZXN0dXNlciIsImV4cCI6MTc1MTcwODc4MH0.LsePev36MG67MbsKI6K8W5VERu1al1QqrEc-CszzZ4I"
	// 解析token
	parsedClaims := ParseToken(tokenStr)

	// 验证解析结果
	assert.Equal(t, claims.ID, parsedClaims.ID, "用户ID应该匹配")
	assert.Equal(t, claims.Accountid, parsedClaims.Accountid, "账号ID应该匹配")
	assert.Equal(t, claims.Name, parsedClaims.Name, "用户名应该匹配")
	assert.Equal(t, claims.Username, parsedClaims.Username, "用户名应该匹配")
}

// 测试token过期
func TestTokenExpiration(t *testing.T) {
	// 创建测试用户信息
	claims := &UserClaims{
		ID:        1,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}

	// 生成token
	tokenStr := GenerateToken(claims)

	// 解析token
	parsedClaims := ParseToken(tokenStr)

	// 验证过期时间
	expectedExpiration := time.Now().Add(effectTime).Unix()
	assert.InDelta(t, expectedExpiration, parsedClaims.ExpiresAt, 2, "过期时间应该在预期范围内")
}

// 测试刷新token
func TestRefreshToken(t *testing.T) {
	// 创建测试用户信息
	claims := &UserClaims{
		ID:        1,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}

	// 生成原始token
	originalTokenStr := GenerateToken(claims)

	// 等待1秒确保时间戳不同
	time.Sleep(1 * time.Second)

	// 刷新token
	refreshedTokenStr := Refresh(originalTokenStr)

	// 验证刷新后的token不为空
	assert.NotEmpty(t, refreshedTokenStr, "刷新后的token不应为空")

	// 解析原始和刷新后的token
	originalClaims := ParseToken(originalTokenStr)
	refreshedClaims := ParseToken(refreshedTokenStr)

	// 验证刷新后的过期时间更新了
	assert.Greater(t, refreshedClaims.ExpiresAt, originalClaims.ExpiresAt, "刷新后的过期时间应该更新")

	// 验证用户信息保持不变
	assert.Equal(t, claims.ID, refreshedClaims.ID, "用户ID应该保持不变")
	assert.Equal(t, claims.Accountid, refreshedClaims.Accountid, "账号ID应该保持不变")
	assert.Equal(t, claims.Name, refreshedClaims.Name, "用户名应该保持不变")
	assert.Equal(t, claims.Username, refreshedClaims.Username, "用户名应该保持不变")
}

// 测试无效token
func TestInvalidToken(t *testing.T) {
	// 测试空token
	assert.Panics(t, func() {
		ParseToken("")
	}, "空token应该触发panic")

	// 测试无效token
	assert.Panics(t, func() {
		ParseToken("invalid.token.here")
	}, "无效token应该触发panic")

	// 测试过期token - 手动创建过期token
	expiredClaims := &UserClaims{
		ID:        1,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}
	expiredClaims.ExpiresAt = time.Now().Add(-time.Hour).Unix() // 设置过期时间为1小时前

	// 手动创建过期token，绕过GenerateToken的自动时间设置
	expiredToken, err := jwt.NewWithClaims(jwt.SigningMethodHS256, expiredClaims).SignedString([]byte("16849841325189456f489"))
	assert.NoError(t, err, "创建过期token不应该出错")

	assert.Panics(t, func() {
		ParseToken(expiredToken)
	}, "过期token应该触发panic")
}

// 测试JwtVerify中间件
func TestJwtVerify(t *testing.T) {
	// 设置gin为测试模式
	gin.SetMode(gin.TestMode)

	// 测试无token情况
	w1, _ := gin.CreateTestContext(nil)
	w1.Request = &http.Request{
		Header: make(http.Header),
	}
	w1.Request.URL = &url.URL{Path: "/test"}

	assert.Panics(t, func() {
		JwtVerify(w1)
	}, "无token应该触发panic")

	// 创建有效token
	claims := &UserClaims{
		ID:        1,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}
	tokenStr := GenerateToken(claims)

	// 测试有效token
	w2, _ := gin.CreateTestContext(nil)
	w2.Request = &http.Request{
		Header: make(http.Header),
	}
	w2.Request.URL = &url.URL{Path: "/test"}
	w2.Request.Header.Set("Authorization", tokenStr)

	assert.NotPanics(t, func() {
		JwtVerify(w2)
	}, "有效token不应该触发panic")

	// 验证用户信息被正确设置
	user, exists := w2.Get("user")
	assert.True(t, exists, "用户信息应该被设置到上下文中")
	assert.IsType(t, &UserClaims{}, user, "用户信息应该是UserClaims类型")
}

// 测试IsContain函数
func TestIsContain(t *testing.T) {
	items := []string{"item1", "item2", "item3"}

	// 测试存在的项
	assert.True(t, IsContain(items, "item1"), "应该找到存在的项")
	assert.True(t, IsContain(items, "item2"), "应该找到存在的项")
	assert.True(t, IsContain(items, "item3"), "应该找到存在的项")

	// 测试不存在的项
	assert.False(t, IsContain(items, "item4"), "不应该找到不存在的项")
	assert.False(t, IsContain(items, ""), "不应该找到空字符串")
}

// 测试TokenOutTime函数
func TestTokenOutTime(t *testing.T) {
	claims := &UserClaims{
		ID:        1,
		Accountid: 100,
		Name:      "测试用户",
		Username:  "testuser",
	}

	// 获取过期时间
	outTime := TokenOutTime(claims)

	// 验证过期时间
	expectedOutTime := time.Now().Add(effectTime).Unix()
	assert.InDelta(t, expectedOutTime, outTime, 2, "过期时间应该在预期范围内")
}
