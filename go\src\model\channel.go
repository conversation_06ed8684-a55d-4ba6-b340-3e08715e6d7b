package model

import (
	"encoding/json"
	"fincore/utils/gf"
	"fincore/utils/gform"
	"fincore/utils/pagination"
	"fmt"
	"strconv"
	"strings"
	"time"
)

const (
	ChannelStatusDisabled = 0
	ChannelStatusEnabled  = 1
)

// LoanRule 放款规则结构
type LoanRule struct {
	RuleID       int           `json:"rule_id"`                // 规则ID，关联product_rules表中的id
	MinRiskScore float64       `json:"min_risk_score"`         // 风控阈值下限
	MaxRiskScore float64       `json:"max_risk_score"`         // 风控阈值上限
	ProductRule  *ProductRules `json:"product_rule,omitempty"` // 关联的产品规则信息
}

// Channel 渠道数据模型
type Channel struct {
	ID                int     `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	ChannelName       string  `json:"channel_name" db:"channel_name"`
	ChannelCode       string  `json:"channel_code" db:"channel_code"`
	Mobile            string  `json:"mobile" db:"mobile"`
	RiskLevel         int     `json:"risk_level" db:"risk_level"`
	AutoLabel         int     `json:"auto_label" db:"auto_label"`
	ChannelStatus     int     `json:"channel_status" db:"channel_status"`
	LoanRules         string  `json:"loan_rules" db:"loan_rules"`
	RiskControl1Limit float64 `json:"risk_control_1_limit" db:"risk_control_1_limit"`
	RiskControl1Upper float64 `json:"risk_control_1_upper" db:"risk_control_1_upper"`
	RiskControl2Limit float64 `json:"risk_control_2_limit" db:"risk_control_2_limit"`
	RiskControl2Upper float64 `json:"risk_control_2_upper" db:"risk_control_2_upper"`
	PointAmount1      float64 `json:"point_amount_1" db:"point_amount_1"`
	PointAmount2      float64 `json:"point_amount_2" db:"point_amount_2"`
	TotalAmount       float64 `json:"total_amount" db:"total_amount"`
	Remark            string  `json:"remark" db:"remark"`
	CreateTime        string  `json:"create_time" db:"create_time"`
	UpdateTime        string  `json:"update_time" db:"update_time"`
}

func NewChannel() *Channel {
	return &Channel{}
}

// GetChannelByCode 根据 code获取渠道信息
func (c *Channel) GetChannelByCode(code string) (resChannel Channel, err error) {
	data, err := DB().Table("channel").Where("channel_code", code).First()
	if err != nil {
		return
	}
	if len(data) == 0 {
		return resChannel, fmt.Errorf("渠道不存在")
	}

	// 数据映射
	var channel Channel
	if err := mapToStruct(data, &channel); err != nil {
		return resChannel, fmt.Errorf("数据映射失败: %v", err)
	}
	return channel, nil
}

// GetLoanRules 获取放款规则列表（包含产品规则信息）
func (c *Channel) GetLoanRules() ([]LoanRule, error) {
	if c.LoanRules == "" {
		return []LoanRule{}, nil
	}

	var rules []LoanRule
	err := json.Unmarshal([]byte(c.LoanRules), &rules)
	if err != nil {
		return nil, fmt.Errorf("解析放款规则失败: %v", err)
	}

	// 获取产品规则服务
	productRuleService := GetProductRulesService()

	// 为每个规则加载关联的产品规则信息
	for i := range rules {
		if rules[i].RuleID > 0 {
			productRule, err := productRuleService.GetProductRuleByID(rules[i].RuleID)
			if err != nil {
				// 如果产品规则不存在，记录错误但不中断流程
				fmt.Printf("警告：产品规则ID %d 不存在: %v\n", rules[i].RuleID, err)
				continue
			}
			rules[i].ProductRule = productRule
		}
	}

	return rules, nil
}

// SetLoanRules 设置放款规则列表
func (c *Channel) SetLoanRules(rules []LoanRule) error {
	rulesJSON, err := json.Marshal(rules)
	if err != nil {
		return fmt.Errorf("序列化放款规则失败: %v", err)
	}

	c.LoanRules = string(rulesJSON)
	return nil
}

// IsAutoDisbursement 判断是否为自动放款渠道
func (c *Channel) IsAutoDisbursement() bool {
	return c.AutoLabel == 0
}

// SetAutoDisbursement 设置自动放款状态
func (c *Channel) SetAutoDisbursement(enabled bool) {
	if enabled {
		c.AutoLabel = 1
	} else {
		c.AutoLabel = 0
	}
}

// GetDisbursementMode 获取放款模式描述
func (c *Channel) GetDisbursementMode() string {
	if c.IsAutoDisbursement() {
		return "自动放款"
	}
	return "人工审核"
}

// mapToProductRules 专门用于将数据映射到ProductRules结构体，处理类型转换
func mapToProductRules(data gform.Data, productRules *ProductRules) error {
	// 辅助函数：安全地转换为int
	parseInt := func(v interface{}) int {
		switch val := v.(type) {
		case int:
			return val
		case int64:
			return int(val)
		case float64:
			return int(val)
		case string:
			if i, err := strconv.Atoi(val); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为float64
	parseFloat := func(v interface{}) float64 {
		switch val := v.(type) {
		case float64:
			return val
		case float32:
			return float64(val)
		case int:
			return float64(val)
		case int64:
			return float64(val)
		case string:
			if f, err := strconv.ParseFloat(val, 64); err == nil {
				return f
			}
		}
		return 0.0
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		productRules.ID = parseInt(v)
	}
	if v, ok := data["rule_name"]; ok {
		productRules.RuleName = parseString(v)
	}
	if v, ok := data["loan_amount"]; ok {
		productRules.LoanAmount = parseFloat(v)
	}
	if v, ok := data["loan_period"]; ok {
		productRules.LoanPeriod = parseInt(v)
	}
	if v, ok := data["total_periods"]; ok {
		productRules.TotalPeriods = parseInt(v)
	}
	if v, ok := data["guarantee_fee"]; ok {
		productRules.GuaranteeFee = parseFloat(v)
	}
	if v, ok := data["annual_interest_rate"]; ok {
		productRules.AnnualInterestRate = parseFloat(v)
	}
	if v, ok := data["other_fees"]; ok {
		productRules.OtherFees = parseFloat(v)
	}
	if v, ok := data["rule_category"]; ok {
		productRules.RuleCategory = parseString(v)
	}
	if v, ok := data["repayment_method"]; ok {
		productRules.RepaymentMethod = parseString(v)
	}
	if v, ok := data["created_at"]; ok {
		productRules.CreatedAt = int64(parseFloat(v))
	}
	if v, ok := data["updated_at"]; ok {
		productRules.UpdatedAt = int64(parseFloat(v))
	}

	return nil
}

// ChannelService 渠道服务
type ChannelService struct{}

// NewChannelService 创建渠道服务实例
func NewChannelService() *ChannelService {
	return &ChannelService{}
}

// GenerateChannelCode 生成10位随机渠道编码
func (s *ChannelService) GenerateChannelCode() (string, error) {
	const codeLength = 10

	for attempts := 0; attempts < 100; attempts++ { // 最多尝试100次
		// 使用gf包生成随机字符串
		code := gf.RandString(codeLength)

		// 转为大写（因为gf.RandString包含小写字母，但我们只要大写）
		code = strings.ToUpper(code)

		// 检查编码是否已存在
		existing, _ := s.GetChannelByCode(code)
		if existing == nil {
			return code, nil
		}
	}

	return "", fmt.Errorf("生成唯一渠道编码失败，请重试")
}

// GetChannelList 获取渠道列表（带分页）
// applyFilters 应用过滤条件到查询对象
func (s *ChannelService) applyFilters(query gform.IOrm, filters map[string]interface{}) gform.IOrm {
	fmt.Printf("DEBUG: applyFilters called with filters: %+v\n", filters)

	if channelName, ok := filters["channel_name"]; ok && channelName != "" {
		query = query.Where("channel_name", "like", fmt.Sprintf("%%%v%%", channelName))
		fmt.Printf("DEBUG: Applied channel_name filter: %v\n", channelName)
	}
	if channelCode, ok := filters["channel_code"]; ok && channelCode != "" {
		query = query.Where("channel_code", "like", fmt.Sprintf("%%%v%%", channelCode))
		fmt.Printf("DEBUG: Applied channel_code filter: %v\n", channelCode)
	}
	if channelStatus, ok := filters["channel_status"]; ok && channelStatus != "" {
		query = query.Where("channel_status", channelStatus)
		fmt.Printf("DEBUG: Applied channel_status filter: %v\n", channelStatus)
	}
	if channelUsage, ok := filters["channel_usage"]; ok && channelUsage != "" {
		query = query.Where("channel_usage", channelUsage)
		fmt.Printf("DEBUG: Applied channel_usage filter: %v\n", channelUsage)
	}
	if mobile, ok := filters["mobile"]; ok && mobile != "" {
		query = query.Where("mobile", "like", fmt.Sprintf("%%%v%%", mobile))
		fmt.Printf("DEBUG: Applied mobile filter: %v\n", mobile)
	}
	if startTime, ok := filters["start_time"]; ok && startTime != "" {
		startTimeStr := fmt.Sprintf("%v 00:00:00", startTime)
		query = query.Where("create_time", ">=", startTimeStr)
		fmt.Printf("DEBUG: Applied start_time filter: %v (converted to: %v)\n", startTime, startTimeStr)
	}
	if endTime, ok := filters["end_time"]; ok && endTime != "" {
		endTimeStr := fmt.Sprintf("%v 23:59:59", endTime)
		query = query.Where("create_time", "<=", endTimeStr)
		fmt.Printf("DEBUG: Applied end_time filter: %v (converted to: %v)\n", endTime, endTimeStr)
	}
	return query
}

func (s *ChannelService) GetChannelList(filters map[string]interface{}, paginationReq pagination.PaginationRequest) (*pagination.PaginationResponse, error) {
	// 创建两个独立的查询对象，避免Count()污染数据查询
	countQuery := s.applyFilters(DB().Table("channel"), filters)
	dataQuery := s.applyFilters(DB().Table("channel"), filters).OrderBy("create_time DESC")

	// 使用自定义分页查询，避免ORM对象污染
	result, err := pagination.PaginateWithCustomQuery(countQuery, dataQuery, paginationReq)
	if err != nil {
		return nil, err
	}

	// 转换数据格式
	if rawData, ok := result.Data.([]gform.Data); ok {
		var channels []Channel
		for _, item := range rawData {
			var channel Channel
			if err := mapToStruct(item, &channel); err != nil {
				return nil, fmt.Errorf("数据映射失败: %v", err)
			}
			channels = append(channels, channel)
		}
		result.Data = channels
	}

	return result, nil
}

// GetActiveChannels 获取所有活跃的渠道
func (s *ChannelService) GetActiveChannels() ([]Channel, error) {
	var channels []Channel
	data, err := DB().Table("channel").Where("channel_status", 1).Get()
	if err != nil {
		return nil, fmt.Errorf("查询活跃渠道失败: %v", err)
	}

	for _, item := range data {
		var channel Channel
		if err := mapToStruct(item, &channel); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		channels = append(channels, channel)
	}

	return channels, nil
}

// GetChannelByID 根据ID获取渠道详情
func (s *ChannelService) GetChannelByID(id int) (*Channel, error) {
	var channel Channel
	data, err := DB().Table("channel").Where("id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询渠道失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("渠道不存在")
	}

	// 数据映射
	if err := mapToStruct(data, &channel); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &channel, nil
}

// GetChannelByCode 根据渠道编码获取渠道详情
func (s *ChannelService) GetChannelByCode(code string) (*Channel, error) {
	var channel Channel
	data, err := DB().Table("channel").Where("channel_code", code).First()
	if err != nil {
		return nil, fmt.Errorf("查询渠道失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("渠道不存在")
	}

	// 数据映射
	if err := mapToStruct(data, &channel); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &channel, nil
}

// CreateChannel 创建渠道
func (s *ChannelService) CreateChannel(channel *Channel) error {
	// 如果没有提供渠道编码，自动生成
	if channel.ChannelCode == "" {
		generatedCode, err := s.GenerateChannelCode()
		if err != nil {
			return fmt.Errorf("生成渠道编码失败: %v", err)
		}
		channel.ChannelCode = generatedCode
	} else {
		// 检查渠道编码是否已存在
		existing, _ := s.GetChannelByCode(channel.ChannelCode)
		if existing != nil {
			return fmt.Errorf("渠道编码已存在")
		}
	}

	// 设置创建时间
	now := time.Now().Format("2006-01-02 15:04:05")
	channel.CreateTime = now
	channel.UpdateTime = now

	// 构建插入数据
	data := map[string]interface{}{
		"channel_name":         channel.ChannelName,
		"channel_code":         channel.ChannelCode,
		"mobile":               channel.Mobile,
		"risk_level":           channel.RiskLevel,
		"auto_label":           channel.AutoLabel,
		"channel_status":       channel.ChannelStatus,
		"loan_rules":           channel.LoanRules,
		"risk_control_1_limit": channel.RiskControl1Limit,
		"risk_control_1_upper": channel.RiskControl1Upper,
		"risk_control_2_limit": channel.RiskControl2Limit,
		"risk_control_2_upper": channel.RiskControl2Upper,
		"point_amount_1":       channel.PointAmount1,
		"point_amount_2":       channel.PointAmount2,
		"total_amount":         channel.TotalAmount,
		"remark":               channel.Remark,
		"create_time":          channel.CreateTime,
		"update_time":          channel.UpdateTime,
	}

	_, err := DB().Table("channel").Insert(data)
	if err != nil {
		return fmt.Errorf("创建渠道失败: %v", err)
	}

	return nil
}

// UpdateChannel 更新渠道
func (s *ChannelService) UpdateChannel(id int, channel *Channel) error {
	// 检查渠道是否存在
	existing, err := s.GetChannelByID(id)
	if err != nil {
		return err
	}
	if existing == nil {
		return fmt.Errorf("渠道不存在")
	}

	// 如果修改了渠道编码，需要检查新编码是否已存在
	if channel.ChannelCode != existing.ChannelCode {
		existingByCode, _ := s.GetChannelByCode(channel.ChannelCode)
		if existingByCode != nil && existingByCode.ID != id {
			return fmt.Errorf("渠道编码已存在")
		}
	}

	// 设置更新时间
	channel.UpdateTime = time.Now().Format("2006-01-02 15:04:05")

	// 构建更新数据
	data := map[string]interface{}{
		"channel_name":         channel.ChannelName,
		"mobile":               channel.Mobile,
		"risk_level":           channel.RiskLevel,
		"auto_label":           channel.AutoLabel,
		"channel_status":       channel.ChannelStatus,
		"loan_rules":           channel.LoanRules,
		"risk_control_1_limit": channel.RiskControl1Limit,
		"risk_control_1_upper": channel.RiskControl1Upper,
		"risk_control_2_limit": channel.RiskControl2Limit,
		"risk_control_2_upper": channel.RiskControl2Upper,
		"point_amount_1":       channel.PointAmount1,
		"point_amount_2":       channel.PointAmount2,
		"total_amount":         channel.TotalAmount,
		"remark":               channel.Remark,
		"update_time":          channel.UpdateTime,
	}

	_, err = DB().Table("channel").Where("id", id).Update(data)
	if err != nil {
		return fmt.Errorf("更新渠道失败: %v", err)
	}

	return nil
}

// DeleteChannel 删除渠道
func (s *ChannelService) DeleteChannel(id int) error {
	// 检查渠道是否存在
	existing, err := s.GetChannelByID(id)
	if err != nil {
		return err
	}
	if existing == nil {
		return fmt.Errorf("渠道不存在")
	}

	// 检查是否有关联的用户（通过business_app_account.go实现）
	hasCustomer, err := NewBusinessAppAccountService().CheckChannelHasBusinessAppAccounts(id)
	if err != nil {
		return err
	}
	if hasCustomer {
		return fmt.Errorf("该渠道下还有关联用户，无法删除")
	}

	// 执行删除
	_, err = DB().Table("channel").Where("id", id).Delete()
	if err != nil {
		return fmt.Errorf("删除渠道失败: %v", err)
	}

	return nil
}

// ChannelOption 渠道选项结构
type ChannelOption struct {
	ID   int    `json:"id"`   // 渠道ID
	Name string `json:"name"` // 渠道名称
}

// GetChannelOptions 获取渠道选项列表（用于下拉框等）
func (s *ChannelService) GetChannelOptions() ([]ChannelOption, error) {
	data, err := DB().Table("channel").
		Fields("id, channel_name").
		Where("channel_status", 1). // 只返回启用状态的渠道
		Order("channel_name ASC").
		Get()

	if err != nil {
		return nil, fmt.Errorf("获取渠道选项失败: %v", err)
	}

	var options []ChannelOption
	for _, item := range data {
		option := ChannelOption{
			ID:   int(item["id"].(int64)),
			Name: item["channel_name"].(string),
		}
		options = append(options, option)
	}

	return options, nil
}

// UpdateChannelStats 更新渠道统计数据
func (s *ChannelService) UpdateChannelStats(channelID int, stats map[string]interface{}) error {
	_, err := DB().Table("channel").Where("id", channelID).Update(stats)
	if err != nil {
		return fmt.Errorf("更新渠道统计失败: %v", err)
	}

	return nil
}

// mapToStruct 将map数据映射到结构体，支持类型转换
func mapToStruct(data gform.Data, result interface{}) error {
	// 对于Channel结构体，我们进行特殊处理
	if channel, ok := result.(*Channel); ok {
		return mapToChannel(data, channel)
	}

	// 对于RiskEvaluation结构体，进行特殊处理
	if evaluation, ok := result.(*RiskEvaluation); ok {
		return mapToRiskEvaluation(data, evaluation)
	}

	// 对于RiskRawData结构体，进行特殊处理
	if rawData, ok := result.(*RiskRawData); ok {
		return mapToRiskRawData(data, rawData)
	}

	// 对于ProductRules结构体，进行特殊处理
	if productRules, ok := result.(*ProductRules); ok {
		return mapToProductRules(data, productRules)
	}

	// 对于BusinessBankCards结构体，进行特殊处理
	if bankCard, ok := result.(*BusinessBankCards); ok {
		return mapToBankCard(data, bankCard)
	}

	// 对于其他类型，使用原来的JSON方式
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonData, result)
}

// mapToChannel 专门用于将数据映射到Channel结构体，处理类型转换
func mapToChannel(data gform.Data, channel *Channel) error {
	// 辅助函数：安全地转换为int
	parseInt := func(v interface{}) int {
		switch val := v.(type) {
		case int:
			return val
		case int64:
			return int(val)
		case float64:
			return int(val)
		case string:
			if i, err := strconv.Atoi(val); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为float64
	parseFloat := func(v interface{}) float64 {
		switch val := v.(type) {
		case float64:
			return val
		case float32:
			return float64(val)
		case int:
			return float64(val)
		case int64:
			return float64(val)
		case string:
			if f, err := strconv.ParseFloat(val, 64); err == nil {
				return f
			}
		}
		return 0.0
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 辅助函数：安全地转换时间字符串，处理Go时间格式
	parseTimeString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		timeStr := parseString(v)

		// 如果包含 " +0000 UTC" 后缀，说明是Go时间格式，需要去掉
		if strings.Contains(timeStr, " +0000 UTC") {
			timeStr = strings.Replace(timeStr, " +0000 UTC", "", 1)
		}

		// 尝试解析时间并重新格式化为标准格式
		if t, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
			return t.Format("2006-01-02 15:04:05")
		}

		// 如果解析失败，返回原字符串
		return timeStr
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		channel.ID = parseInt(v)
	}
	if v, ok := data["channel_name"]; ok {
		channel.ChannelName = parseString(v)
	}
	if v, ok := data["channel_code"]; ok {
		channel.ChannelCode = parseString(v)
	}
	if v, ok := data["mobile"]; ok {
		channel.Mobile = parseString(v)
	}
	if v, ok := data["risk_level"]; ok {
		channel.RiskLevel = parseInt(v)
	}
	if v, ok := data["auto_label"]; ok {
		channel.AutoLabel = parseInt(v)
	}
	if v, ok := data["channel_status"]; ok {
		channel.ChannelStatus = parseInt(v)
	}
	if v, ok := data["loan_rules"]; ok {
		channel.LoanRules = parseString(v)
	}
	if v, ok := data["loan_rules"]; ok {
		channel.LoanRules = parseString(v)
	}
	if v, ok := data["risk_control_1_limit"]; ok {
		channel.RiskControl1Limit = parseFloat(v)
	}
	if v, ok := data["risk_control_1_upper"]; ok {
		channel.RiskControl1Upper = parseFloat(v)
	}
	if v, ok := data["risk_control_2_limit"]; ok {
		channel.RiskControl2Limit = parseFloat(v)
	}
	if v, ok := data["risk_control_2_upper"]; ok {
		channel.RiskControl2Upper = parseFloat(v)
	}
	if v, ok := data["point_amount_1"]; ok {
		channel.PointAmount1 = parseFloat(v)
	}
	if v, ok := data["point_amount_2"]; ok {
		channel.PointAmount2 = parseFloat(v)
	}
	if v, ok := data["total_amount"]; ok {
		channel.TotalAmount = parseFloat(v)
	}
	if v, ok := data["remark"]; ok {
		channel.Remark = parseString(v)
	}
	if v, ok := data["create_time"]; ok {
		channel.CreateTime = parseTimeString(v)
	}
	if v, ok := data["update_time"]; ok {
		channel.UpdateTime = parseTimeString(v)
	}

	return nil
}
